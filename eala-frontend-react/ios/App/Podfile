require_relative '../../../node_modules/@capacitor/ios/scripts/pods_helpers'

platform :ios, '14.0'
use_frameworks!

# workaround to avoid Xcode caching of Pods that requires
# Product -> Clean Build Folder after new Cordova plugins installed
# Requires CocoaPods 1.6 or newer
install! 'cocoapods', :disable_input_output_paths => true

def capacitor_pods
  pod 'Capacitor', :path => '../../../node_modules/@capacitor/ios'
  pod 'CapacitorCordova', :path => '../../../node_modules/@capacitor/ios'
  pod 'CapacitorApp', :path => '../../../node_modules/@capacitor/app'
  pod 'CapacitorDevice', :path => '../../../node_modules/@capacitor/device'
  pod 'CapacitorHaptics', :path => '../../../node_modules/@capacitor/haptics'
  pod 'CapacitorKeyboard', :path => '../../../node_modules/@capacitor/keyboard'
  pod 'CapacitorShare', :path => '../../../node_modules/@capacitor/share'
  pod 'CapacitorStatusBar', :path => '../../../node_modules/@capacitor/status-bar'
end

target 'App' do
  capacitor_pods
  # Add your Pods here
end

post_install do |installer|
  assertDeploymentTarget(installer)
end
