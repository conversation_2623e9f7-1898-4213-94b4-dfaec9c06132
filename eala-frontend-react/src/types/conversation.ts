import { Topic } from './topic';

export interface ConversationMessage {
  id: number;
  session_id: string;
  message_type: 'ai' | 'user';
  content: string;
  audio_url?: string;
  transcribed_text?: string;
  created_at: string;
}

export interface ConversationSession {
  id: number;
  session_id: string;
  user_id?: string;
  topic_id: number;
  status: string;
  created_at: string;
  updated_at: string;
  topic?: Topic;
}

export interface ConversationStartResponse {
  session_id: string;
  initial_message: ConversationMessage;
  topic: Topic;
}

export interface ConversationHistoryResponse {
  session: ConversationSession;
  messages: ConversationMessage[];
}

export interface ConversationStartRequest {
  topic_id: number;
  user_id?: string;
}

export interface ConversationRespondRequest {
  student_input_text?: string;
  student_audio_base64?: string;
  audio_format?: string;
  language_code?: string;
}

export interface ConversationRespondResponse {
  ai_response: ConversationMessage;
  transcribed_text?: string;
  transcription_confidence?: number;
  fallback_used?: boolean;
}

export type AudioRecordingState = 'idle' | 'recording' | 'processing' | 'error';
export type MicrophonePermissionState = 'checking' | 'granted' | 'denied' | 'prompt' | 'unavailable';

export interface AudioRecorderState {
  state: AudioRecordingState;
  duration: number;
  audioBlob?: Blob;
  error?: string;
  permissionState: MicrophonePermissionState;
  permissionError?: string;
}

export interface PermissionError {
  type: 'NotAllowedError' | 'NotFoundError' | 'NotSupportedError' | 'AbortError' | 'UnknownError';
  message: string;
  userGuidance: string;
  browserSpecific?: {
    chrome?: string;
    firefox?: string;
    safari?: string;
    edge?: string;
  };
}