export interface PracticeSentenceRequest {
  difficulty?: 'beginner' | 'intermediate' | 'advanced';
  focus_sounds?: string;
}

export interface PracticeSentenceResponse {
  sentence: string;
  difficulty: string;
  focus_sounds?: string;
}

export interface PronunciationAssessmentRequest {
  reference_text: string;
  audio_base64: string;
  audio_format?: string;
  user_id?: string;
}

export interface PronunciationScores {
  overall_score?: number;
  fluency_score?: number;
  accuracy_score?: number;
  completeness_score?: number;
  prosody_score?: number;
}

export interface PronunciationAssessmentResponse {
  assessment_id: string;
  success: boolean;
  scores?: PronunciationScores;
  word_level_feedback?: WordFeedback[];
  overall_feedback?: string;
  error?: string;
  processing_time_ms?: number;
}

export interface WordFeedback {
  word: string;
  accuracy_score?: number;
  error_type?: string;
}

export type PronunciationPracticeStep = 'generate' | 'display' | 'record' | 'assess' | 'feedback';

export interface PronunciationPracticeState {
  currentStep: PronunciationPracticeStep;
  sentence?: string;
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  audioBlob?: Blob;
  assessment?: PronunciationAssessmentResponse;
  error?: string;
  isLoading: boolean;
}

export interface AudioQualityResult {
  isValid: boolean;
  duration: number;
  hasAudioContent: boolean;
  volumeLevel: 'too_quiet' | 'good' | 'too_loud';
  errors: string[];
  warnings: string[];
}