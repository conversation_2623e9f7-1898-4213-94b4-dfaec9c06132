import { Topic } from "../types/topic";
import {
  ConversationStartResponse,
  ConversationHistoryResponse,
  ConversationStartRequest,
  ConversationRespondRequest,
  ConversationRespondResponse,
} from "../types/conversation";
import {
  PracticeSentenceRequest,
  PracticeSentenceResponse,
  PronunciationAssessmentRequest,
  PronunciationAssessmentResponse,
} from "../types/pronunciation";

const API_BASE_URL = import.meta.env.VITE_API_URL || "http://localhost:8121";

// Request timeout for mobile networks
const REQUEST_TIMEOUT = 30000; // 30 seconds

// Request queue for handling multiple requests
const requestQueue = new Map<string, Promise<unknown>>();

// Network status tracking
let isOnline = navigator.onLine;
if (typeof window !== "undefined") {
  window.addEventListener("online", () => {
    isOnline = true;
  });
  window.addEventListener("offline", () => {
    isOnline = false;
  });
}

// Request cancellation controller
let abortController = new AbortController();

class APIError extends Error {
  public status?: number;
  public isNetworkError = false;
  public isTimeoutError = false;

  constructor(
    message: string,
    status?: number,
    isNetworkError = false,
    isTimeoutError = false
  ) {
    super(message);
    this.name = "APIError";
    this.status = status;
    this.isNetworkError = isNetworkError;
    this.isTimeoutError = isTimeoutError;
  }
}

// Enhanced fetch with timeout and retry logic
async function fetchWithTimeout(
  url: string,
  options: RequestInit = {},
  timeout = REQUEST_TIMEOUT
): Promise<Response> {
  const controller = new AbortController();
  const timeoutId = setTimeout(() => controller.abort(), timeout);

  try {
    const response = await fetch(url, {
      ...options,
      signal: controller.signal,
      headers: {
        "Content-Type": "application/json",
        ...options.headers,
      },
    });

    clearTimeout(timeoutId);

    if (!response.ok) {
      throw new APIError(
        `HTTP error! status: ${response.status}`,
        response.status,
        false,
        false
      );
    }

    return response;
  } catch (error) {
    clearTimeout(timeoutId);

    if (error instanceof Error) {
      if (error.name === "AbortError") {
        throw new APIError("Request timed out", 0, false, true);
      }
      if (!isOnline) {
        throw new APIError("No internet connection", 0, true, false);
      }
      throw new APIError(error.message, 0, true, false);
    }

    throw error;
  }
}

// Debounced request to prevent duplicate API calls
function debounceRequest<T>(
  key: string,
  requestFn: () => Promise<T>,
  delay = 300
): Promise<T> {
  if (requestQueue.has(key)) {
    return requestQueue.get(key)! as Promise<T>;
  }

  const promise = new Promise<T>((resolve, reject) => {
    setTimeout(async () => {
      try {
        const result = await requestFn();
        requestQueue.delete(key);
        resolve(result);
      } catch (error) {
        requestQueue.delete(key);
        reject(error);
      }
    }, delay);
  });

  requestQueue.set(key, promise);
  return promise;
}

// Retry logic for failed requests (currently unused but available for future use)
// async function retryRequest<T>(
//   requestFn: () => Promise<T>,
//   maxRetries = 3,
//   delay = 1000
// ): Promise<T> {
//   for (let i = 0; i < maxRetries; i++) {
//     try {
//       return await requestFn();
//     } catch (error) {
//       if (i === maxRetries - 1) throw error;
//
//       // Exponential backoff
//       await new Promise(resolve => setTimeout(resolve, delay * Math.pow(2, i)));
//     }
//   }
//
//   throw new Error('Max retries exceeded');
// }

export class ApiService {
  // Cancel all pending requests
  static cancelAllRequests() {
    abortController.abort();
    abortController = new AbortController();
    requestQueue.clear();
  }

  // Check network status
  static isNetworkAvailable(): boolean {
    return isOnline;
  }

  // Topics
  static async fetchTopics(): Promise<Topic[]> {
    const cacheKey = "topics";

    return debounceRequest(cacheKey, async () => {
      try {
        const response = await fetchWithTimeout(
          `${API_BASE_URL}/api/v1/conversation/topics`
        );
        const topics: Topic[] = await response.json();

        // Cache topics in localStorage for offline access
        localStorage.setItem(
          "cached_topics",
          JSON.stringify({
            data: topics,
            timestamp: Date.now(),
          })
        );

        return topics;
      } catch (error) {
        console.error("Error fetching topics:", error);

        // Try to return cached data if available
        const cached = localStorage.getItem("cached_topics");
        if (cached) {
          const { data, timestamp } = JSON.parse(cached);
          // Use cached data if less than 1 hour old
          if (Date.now() - timestamp < 3600000) {
            console.log("Using cached topics data");
            return data;
          }
        }

        throw error;
      }
    });
  }

  // Conversations
  static async startConversation(
    topicId: number,
    userId?: string
  ): Promise<ConversationStartResponse> {
    try {
      const requestBody: ConversationStartRequest = {
        topic_id: topicId,
        user_id: userId,
      };

      const response = await fetchWithTimeout(
        `${API_BASE_URL}/api/v1/conversation/start`,
        {
          method: "POST",
          body: JSON.stringify(requestBody),
        }
      );

      const conversationData: ConversationStartResponse = await response.json();
      return conversationData;
    } catch (error) {
      console.error("Error starting conversation:", error);
      throw error;
    }
  }

  static async getConversation(
    sessionId: string
  ): Promise<ConversationHistoryResponse> {
    try {
      const response = await fetchWithTimeout(
        `${API_BASE_URL}/api/v1/conversation/${sessionId}`
      );
      const conversationData: ConversationHistoryResponse =
        await response.json();
      return conversationData;
    } catch (error) {
      console.error("Error fetching conversation:", error);
      throw error;
    }
  }

  static async respondToConversation(
    sessionId: string,
    request: ConversationRespondRequest
  ): Promise<ConversationRespondResponse> {
    try {
      const response = await fetchWithTimeout(
        `${API_BASE_URL}/api/v1/conversation/${sessionId}/respond`,
        {
          method: "POST",
          body: JSON.stringify(request),
        }
      );

      const responseData: ConversationRespondResponse = await response.json();
      return responseData;
    } catch (error) {
      console.error("Error responding to conversation:", error);
      throw error;
    }
  }

  // Audio utilities
  static async audioToBase64(audioBlob: Blob): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => {
        const result = reader.result as string;
        const base64 = result.split(",")[1];
        resolve(base64);
      };
      reader.onerror = reject;
      reader.readAsDataURL(audioBlob);
    });
  }

  // Enhanced audio validation for mobile
  static validateAudioBlob(audioBlob: Blob): {
    isValid: boolean;
    errors: string[];
  } {
    const errors: string[] = [];

    if (!audioBlob || audioBlob.size === 0) {
      errors.push("Audio data is empty");
    }

    if (audioBlob.size < 1000) {
      errors.push("Audio data is too small (less than 1KB)");
    }

    if (audioBlob.size > 10 * 1024 * 1024) {
      errors.push("Audio data is too large (over 10MB)");
    }

    if (!audioBlob.type.includes("audio") && !audioBlob.type.includes("webm")) {
      errors.push("Invalid audio format");
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  // Pronunciation practice
  static async generatePracticeSentence(
    request: PracticeSentenceRequest
  ): Promise<PracticeSentenceResponse> {
    try {
      const response = await fetchWithTimeout(
        `${API_BASE_URL}/api/v1/practice/generate-sentence`,
        {
          method: "POST",
          body: JSON.stringify(request),
        }
      );

      const sentenceData: PracticeSentenceResponse = await response.json();
      return sentenceData;
    } catch (error) {
      console.error("Error generating practice sentence:", error);
      throw error;
    }
  }

  static async assessPronunciation(
    audioBlob: Blob,
    referenceText: string,
    userId?: string
  ): Promise<PronunciationAssessmentResponse> {
    try {
      // Validate audio before processing
      const validation = ApiService.validateAudioBlob(audioBlob);
      if (!validation.isValid) {
        throw new APIError(
          `Audio validation failed: ${validation.errors.join(", ")}`
        );
      }

      const audioBase64 = await ApiService.audioToBase64(audioBlob);
      const audioFormat = audioBlob.type.includes("webm")
        ? "webm"
        : audioBlob.type.includes("mp4")
        ? "mp4"
        : "webm";

      const requestBody: PronunciationAssessmentRequest = {
        reference_text: referenceText,
        audio_base64: audioBase64,
        audio_format: audioFormat,
        user_id: userId || "default_user",
      };

      console.log("=== MOBILE API SERVICE DEBUG ===");
      console.log("Request body:", requestBody);
      console.log("Audio blob size:", audioBlob.size);
      console.log("Audio format:", audioFormat);

      const response = await fetchWithTimeout(
        `${API_BASE_URL}/api/v1/practice/assess-pronunciation`,
        {
          method: "POST",
          body: JSON.stringify(requestBody),
        }
      );

      const assessmentData: PronunciationAssessmentResponse =
        await response.json();
      console.log("Assessment response:", assessmentData);
      console.log("=== END MOBILE API DEBUG ===");

      return assessmentData;
    } catch (error) {
      console.error("Error assessing pronunciation:", error);
      throw error;
    }
  }

  // Debug audio saving (for development)
  static async saveAudioForDebug(audioBlob: Blob): Promise<unknown> {
    try {
      const validation = ApiService.validateAudioBlob(audioBlob);
      if (!validation.isValid) {
        console.warn(
          "Audio validation failed for debug save:",
          validation.errors
        );
      }

      const audioBase64 = await ApiService.audioToBase64(audioBlob);
      const audioFormat = audioBlob.type.includes("webm")
        ? "webm"
        : audioBlob.type.includes("mp4")
        ? "mp4"
        : "webm";

      const response = await fetchWithTimeout(
        `${API_BASE_URL}/api/v1/conversation/debug/save-audio`,
        {
          method: "POST",
          body: JSON.stringify({
            student_audio_base64: audioBase64,
            audio_format: audioFormat,
            language_code: "en-US",
          }),
        }
      );

      return await response.json();
    } catch (error) {
      console.error("Failed to save audio for debug:", error);
      return null;
    }
  }
}

// Export convenience functions
export const {
  fetchTopics,
  startConversation,
  getConversation,
  respondToConversation,
  generatePracticeSentence,
  assessPronunciation,
  audioToBase64,
  validateAudioBlob,
  saveAudioForDebug,
  cancelAllRequests,
  isNetworkAvailable,
} = ApiService;
