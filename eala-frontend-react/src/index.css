@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  /* Design system color variables */
  --primary-blue: #6366F1;
  --primary-dark-blue: #4F46E5;
  --primary-light-blue: #818CF8;
  --secondary-orange: #F97316;
  --secondary-coral: #FF6B6B;
  --secondary-yellow: #FCD34D;
  --accent-green: #10B981;
  --accent-teal: #14B8A6;
  --accent-purple: #8B5CF6;
  --neutral-white: #FFFFFF;
  --neutral-light-gray: #F8FAFC;
  --neutral-medium-gray: #E2E8F0;
  --neutral-dark-gray: #64748B;
  --neutral-text: #334155;
  
  /* Legacy variables for compatibility */
  --background: #F8FAFC;
  --foreground: #334155;
  --font-geist-sans: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --font-geist-mono: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
}

* {
  box-sizing: border-box;
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-geist-sans);
  font-weight: 400;
  margin: 0;
  padding: 0;
  overflow-x: hidden;
  /* Prevent horizontal scrolling */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Mobile-first responsive typography */
html {
  font-size: 14px; /* Mobile base size */
  background: var(--background);
}

@media (min-width: 640px) {
  html {
    font-size: 16px; /* Desktop base size */
  }
}

/* Touch improvements */
button, [role="button"] {
  -webkit-tap-highlight-color: transparent;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* Improve text selection on mobile */
input, textarea {
  -webkit-user-select: text;
  -moz-user-select: text;
  -ms-user-select: text;
  user-select: text;
}

/* Safe area handling for mobile */
.safe-area-top {
  padding-top: env(safe-area-inset-top);
}

.safe-area-bottom {
  padding-bottom: env(safe-area-inset-bottom);
}

.safe-area-left {
  padding-left: env(safe-area-inset-left);
}

.safe-area-right {
  padding-right: env(safe-area-inset-right);
}

/* Custom scrollbar for better mobile experience */
::-webkit-scrollbar {
  width: 4px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 2px;
}

::-webkit-scrollbar-thumb:hover {
  background: #555;
}

/* Loading animation */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.loading-spinner {
  animation: spin 1s linear infinite;
}

/* Design System Components */
@layer components {
  /* Main Learning Card */
  .learning-card {
    @apply bg-gradient-primary-card text-white rounded-2xl p-6 shadow-card-strong;
  }

  /* Course Discovery Card */
  .course-card {
    @apply bg-white rounded-2xl p-6 shadow-card-soft border border-neutral-medium-gray;
  }

  /* Subject Cards */
  .subject-card {
    @apply bg-white rounded-lg p-4 shadow-card-soft border border-neutral-medium-gray;
  }

  /* Primary Button */
  .btn-primary {
    @apply bg-gradient-button-primary text-white font-semibold px-8 py-4 rounded-lg shadow-button-default min-h-touch;
    transition: all 150ms ease-in-out;
  }

  .btn-primary:hover {
    @apply bg-gradient-button-primary-hover shadow-button-hover;
    transform: translateY(-2px);
  }

  .btn-primary:active {
    transform: translateY(0);
    @apply shadow-button-default;
  }

  /* Promotional Button */
  .btn-promotional {
    @apply bg-secondary-yellow text-yellow-900 font-semibold px-6 py-3 rounded-md shadow-card-soft;
  }

  /* Unlock Button */
  .btn-unlock {
    @apply bg-gradient-unlock-button text-white font-semibold px-6 py-4 rounded-lg min-h-touch;
  }

  /* Promotional Banner */
  .banner-promotional {
    @apply bg-gradient-promotional text-white rounded-lg p-5;
  }

  /* Navigation Tab */
  .tab-nav {
    @apply min-h-touch min-w-touch px-4 py-3 rounded-lg transition-all duration-200 flex flex-col items-center justify-center;
    transform: translateY(0);
  }

  .tab-nav.active {
    @apply bg-primary-blue text-white shadow-lg;
    transform: translateY(-1px);
  }

  .tab-nav.inactive {
    @apply text-neutral-dark-gray hover:bg-neutral-light-gray/80 hover:shadow-sm;
  }

  .tab-nav:active {
    transform: translateY(0);
  }

  /* Floating Navigation Enhancement */
  .tab-nav.inactive:hover {
    transform: translateY(-1px);
  }

  /* Liquid Glass Navigation Tab */
  .tab-nav-liquid {
    @apply min-h-touch min-w-touch px-4 py-3 rounded-2xl transition-all duration-300 flex flex-col items-center justify-center relative;
    transform: translateY(0);
  }

  .tab-nav-liquid.active {
    @apply bg-primary-blue/90 text-white shadow-lg;
    transform: translateY(-1px) scale(1.02);
    backdrop-filter: blur(10px);
    box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.3), 0 4px 16px rgba(99, 102, 241, 0.4);
  }

  .tab-nav-liquid.active::before {
    content: '';
    position: absolute;
    inset: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0.05) 100%);
    border-radius: 1rem;
    pointer-events: none;
  }

  .tab-nav-liquid.inactive {
    @apply text-neutral-dark-gray;
  }

  .tab-nav-liquid.inactive:hover {
    @apply bg-white/15;
    transform: translateY(-1px) scale(1.01);
    backdrop-filter: blur(5px);
  }

  .tab-nav-liquid:active {
    transform: translateY(0) scale(0.98);
  }

  /* Icon Containers */
  .icon-container {
    @apply w-12 h-12 rounded-md flex items-center justify-center;
  }

  .icon-container.mathematics {
    @apply bg-gradient-mathematics text-white;
  }

  .icon-container.chemistry {
    @apply bg-gradient-chemistry text-white;
  }

  .icon-container.biology {
    @apply bg-gradient-biology text-white;
  }

  .icon-container.language {
    @apply bg-gradient-language text-white;
  }

  /* Typography Classes */
  .heading-primary {
    @apply text-white text-3xl font-bold leading-tight;
  }

  .heading-secondary {
    @apply text-neutral-text text-2xl font-semibold leading-tight;
  }

  .body-text {
    @apply text-neutral-dark-gray text-base leading-relaxed;
  }

  .caption-text {
    @apply text-neutral-dark-gray text-sm font-medium;
  }

  .price-text {
    @apply text-white text-xl font-bold;
  }

  .subject-title {
    @apply text-neutral-text text-base font-semibold;
  }

  .subject-price {
    @apply text-neutral-dark-gray text-sm font-medium;
  }

  /* Card Hover Effects */
  .card-hover {
    transition: all 150ms ease-in-out;
  }

  .card-hover:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 32px rgba(99, 102, 241, 0.15);
  }

  /* Mobile-optimized touch targets */
  .touch-target {
    @apply min-h-touch min-w-touch;
  }
}

/* Legacy Brutal UI Components (for backward compatibility) */
.brutal-button {
  background-color: #60a5fa;
  color: #000000;
  font-weight: 700;
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  border: 2px solid #000000;
  box-shadow: 2px 2px 0px #000;
  transition: all 150ms;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-height: 44px;
}

.brutal-button:hover,
.brutal-button:active {
  box-shadow: none;
  transform: translate(2px, 2px);
}

.brutal-card {
  background-color: #ffffff;
  border-radius: 0.5rem;
  border: 2px solid #000000;
  box-shadow: 4px 4px 0px #000;
}

.brutal-input {
  padding: 0.75rem 1rem;
  border: 2px solid #000000;
  border-radius: 0.5rem;
  background-color: #ffffff;
  outline: none;
  min-height: 44px;
}

.brutal-input:focus {
  box-shadow: 0 0 0 2px #facc15;
}

/* Mobile-optimized line clamping */
.line-clamp-1 {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}