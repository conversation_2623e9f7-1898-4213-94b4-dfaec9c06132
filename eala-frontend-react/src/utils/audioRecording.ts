import { isNativePlatform } from './capacitor';

export interface AudioRecordingResult {
  success: boolean;
  audioBlob?: Blob;
  audioUrl?: string;
  duration?: number;
  error?: string;
}

export interface AudioRecordingOptions {
  maxDuration?: number; // in seconds
  sampleRate?: number;
  echoCancellation?: boolean;
  noiseSuppression?: boolean;
  autoGainControl?: boolean;
}

class AudioRecordingManager {
  private isRecording = false;
  private mediaRecorder: MediaRecorder | null = null;
  private audioChunks: Blob[] = [];
  private stream: MediaStream | null = null;
  private startTime: number = 0;
  private maxDuration: number = 60; // default 60 seconds

  async startRecording(options: AudioRecordingOptions = {}): Promise<boolean> {
    if (this.isRecording) {
      console.warn('Recording already in progress');
      return false;
    }

    this.maxDuration = options.maxDuration || 60;

    if (isNativePlatform()) {
      return this.startNativeRecording();
    } else {
      return this.startWebRecording(options);
    }
  }

  async stopRecording(): Promise<AudioRecordingResult> {
    if (!this.isRecording) {
      return {
        success: false,
        error: 'No recording in progress'
      };
    }

    if (isNativePlatform()) {
      return this.stopNativeRecording();
    } else {
      return this.stopWebRecording();
    }
  }

  private async startNativeRecording(): Promise<boolean> {
    try {
      const { VoiceRecorder } = await import('capacitor-voice-recorder');
      
      // Check permission first
      const hasPermission = await VoiceRecorder.hasAudioRecordingPermission();
      if (!hasPermission.value) {
        const permission = await VoiceRecorder.requestAudioRecordingPermission();
        if (!permission.value) {
          console.warn('Audio recording permission denied');
          return false;
        }
      }

      // Start recording
      await VoiceRecorder.startRecording();
      this.isRecording = true;
      this.startTime = Date.now();

      // Set up auto-stop timer
      setTimeout(() => {
        if (this.isRecording) {
          this.stopRecording();
        }
      }, this.maxDuration * 1000);

      console.log('Native recording started');
      return true;
    } catch (error) {
      console.error('Failed to start native recording:', error);
      return false;
    }
  }

  private async stopNativeRecording(): Promise<AudioRecordingResult> {
    try {
      const { VoiceRecorder } = await import('capacitor-voice-recorder');
      
      const result = await VoiceRecorder.stopRecording();
      this.isRecording = false;
      
      const duration = (Date.now() - this.startTime) / 1000;

      if (result.value && result.value.recordDataBase64) {
        // Convert base64 to blob
        const base64Data = result.value.recordDataBase64;
        const binaryString = atob(base64Data);
        const bytes = new Uint8Array(binaryString.length);
        
        for (let i = 0; i < binaryString.length; i++) {
          bytes[i] = binaryString.charCodeAt(i);
        }
        
        const audioBlob = new Blob([bytes], { type: 'audio/aac' });
        const audioUrl = URL.createObjectURL(audioBlob);

        console.log('Native recording stopped successfully');
        return {
          success: true,
          audioBlob,
          audioUrl,
          duration
        };
      } else {
        return {
          success: false,
          error: 'No audio data received from native recorder'
        };
      }
    } catch (error) {
      this.isRecording = false;
      console.error('Failed to stop native recording:', error);
      return {
        success: false,
        error: `Failed to stop recording: ${error}`
      };
    }
  }

  private async startWebRecording(options: AudioRecordingOptions): Promise<boolean> {
    try {
      this.stream = await navigator.mediaDevices.getUserMedia({
        audio: {
          echoCancellation: options.echoCancellation ?? true,
          noiseSuppression: options.noiseSuppression ?? true,
          autoGainControl: options.autoGainControl ?? true,
          sampleRate: options.sampleRate ?? 44100
        }
      });

      this.audioChunks = [];
      this.mediaRecorder = new MediaRecorder(this.stream);
      
      this.mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          this.audioChunks.push(event.data);
        }
      };

      this.mediaRecorder.start();
      this.isRecording = true;
      this.startTime = Date.now();

      // Set up auto-stop timer
      setTimeout(() => {
        if (this.isRecording) {
          this.stopRecording();
        }
      }, this.maxDuration * 1000);

      console.log('Web recording started');
      return true;
    } catch (error) {
      console.error('Failed to start web recording:', error);
      return false;
    }
  }

  private async stopWebRecording(): Promise<AudioRecordingResult> {
    return new Promise((resolve) => {
      if (!this.mediaRecorder) {
        resolve({
          success: false,
          error: 'No media recorder available'
        });
        return;
      }

      this.mediaRecorder.onstop = () => {
        const duration = (Date.now() - this.startTime) / 1000;
        const audioBlob = new Blob(this.audioChunks, { type: 'audio/webm' });
        const audioUrl = URL.createObjectURL(audioBlob);

        // Clean up
        if (this.stream) {
          this.stream.getTracks().forEach(track => track.stop());
          this.stream = null;
        }
        this.mediaRecorder = null;
        this.audioChunks = [];

        console.log('Web recording stopped successfully');
        resolve({
          success: true,
          audioBlob,
          audioUrl,
          duration
        });
      };

      this.mediaRecorder.stop();
      this.isRecording = false;
    });
  }

  isCurrentlyRecording(): boolean {
    return this.isRecording;
  }

  cleanup(): void {
    if (this.isRecording) {
      this.stopRecording();
    }
    
    if (this.stream) {
      this.stream.getTracks().forEach(track => track.stop());
      this.stream = null;
    }
    
    this.mediaRecorder = null;
    this.audioChunks = [];
  }
}

// Export a singleton instance
export const audioRecorder = new AudioRecordingManager();

// Export utility functions
export const startAudioRecording = (options?: AudioRecordingOptions) => 
  audioRecorder.startRecording(options);

export const stopAudioRecording = () => 
  audioRecorder.stopRecording();

export const isRecording = () => 
  audioRecorder.isCurrentlyRecording();

export const cleanupAudioRecording = () => 
  audioRecorder.cleanup();
