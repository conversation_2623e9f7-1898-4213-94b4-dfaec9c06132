import { Capacitor } from "@capacitor/core";

// Platform detection
export const isNativePlatform = (): boolean => {
  return Capacitor.isNativePlatform();
};

export const getPlatform = (): string => {
  return Capacitor.getPlatform();
};

export const isIOS = (): boolean => {
  return Capacitor.getPlatform() === "ios";
};

export const isAndroid = (): boolean => {
  return Capacitor.getPlatform() === "android";
};

export const isWeb = (): boolean => {
  return Capacitor.getPlatform() === "web";
};

// Safe area utilities
export const getSafeAreaInsets = () => {
  if (!isNativePlatform()) {
    return { top: 0, bottom: 0, left: 0, right: 0 };
  }

  // These will be handled by CSS env() variables in most cases
  // This is a fallback for programmatic usage
  return {
    top: 44, // Typical iOS status bar + notch
    bottom: isIOS() ? 34 : 0, // iOS home indicator
    left: 0,
    right: 0,
  };
};

// Network status
export const isOnline = (): boolean => {
  if (typeof navigator !== "undefined") {
    return navigator.onLine;
  }
  return true;
};

// Device info
export const getDeviceInfo = async () => {
  if (!isNativePlatform()) {
    return {
      model: "Web Browser",
      platform: "web",
      operatingSystem: navigator.userAgent,
      osVersion: "unknown",
      manufacturer: "unknown",
      isVirtual: false,
      webViewVersion: "N/A",
    };
  }

  try {
    const { Device } = await import("@capacitor/device");
    return await Device.getInfo();
  } catch (error) {
    console.warn("Failed to get device info:", error);
    return null;
  }
};

// Haptic feedback (mobile only)
export const hapticImpact = async (
  style: "light" | "medium" | "heavy" = "light"
) => {
  if (!isNativePlatform()) return;

  try {
    const { Haptics, ImpactStyle } = await import("@capacitor/haptics");
    await Haptics.impact({
      style:
        style === "light"
          ? ImpactStyle.Light
          : style === "medium"
          ? ImpactStyle.Medium
          : ImpactStyle.Heavy,
    });
  } catch (error) {
    console.warn("Haptic feedback not available:", error);
  }
};

// Status bar management
export const setStatusBarStyle = async (style: "dark" | "light" = "dark") => {
  if (!isNativePlatform()) return;

  try {
    const { StatusBar, Style } = await import("@capacitor/status-bar");
    await StatusBar.setStyle({
      style: style === "dark" ? Style.Dark : Style.Light,
    });
  } catch (error) {
    console.warn("Status bar control not available:", error);
  }
};

// Keyboard management
export const hideKeyboard = async () => {
  if (!isNativePlatform()) return;

  try {
    const { Keyboard } = await import("@capacitor/keyboard");
    await Keyboard.hide();
  } catch (error) {
    console.warn("Keyboard control not available:", error);
  }
};

// App state management
export const addAppStateChangeListener = (
  callback: (isActive: boolean) => void
) => {
  if (!isNativePlatform()) return () => {};

  let removeListener: (() => void) | null = null;

  import("@capacitor/app")
    .then(({ App }) => {
      App.addListener("appStateChange", ({ isActive }) => {
        callback(isActive);
      }).then((listener) => {
        removeListener = listener.remove;
      });
    })
    .catch((error) => {
      console.warn("App state listener not available:", error);
    });

  return () => {
    if (removeListener) {
      removeListener();
    }
  };
};

// Deep linking
export const addDeepLinkListener = (callback: (url: string) => void) => {
  if (!isNativePlatform()) return () => {};

  let removeListener: (() => void) | null = null;

  import("@capacitor/app")
    .then(({ App }) => {
      App.addListener("appUrlOpen", ({ url }) => {
        callback(url);
      }).then((listener) => {
        removeListener = listener.remove;
      });
    })
    .catch((error) => {
      console.warn("Deep link listener not available:", error);
    });

  return () => {
    if (removeListener) {
      removeListener();
    }
  };
};

// Share functionality
export const shareContent = async (
  title: string,
  text: string,
  url?: string
) => {
  if (!isNativePlatform()) {
    // Fallback to Web Share API
    if (navigator.share) {
      try {
        await navigator.share({ title, text, url });
        return true;
      } catch (error) {
        console.warn("Web share failed:", error);
        return false;
      }
    }
    return false;
  }

  try {
    const { Share } = await import("@capacitor/share");
    await Share.share({ title, text, url });
    return true;
  } catch (error) {
    console.warn("Native share not available:", error);
    return false;
  }
};

// Microphone permission handling for Android
export const requestMicrophonePermission = async (): Promise<boolean> => {
  if (!isNativePlatform()) {
    // For web, use the standard Web API
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      stream.getTracks().forEach((track) => track.stop());
      return true;
    } catch (error) {
      console.warn("Web microphone permission denied:", error);
      return false;
    }
  }

  if (isAndroid()) {
    try {
      // For Android, we need to request permission through the native layer
      // Since Capacitor v7 doesn't have a separate permissions plugin,
      // we'll use the App plugin to check if we can access the microphone
      const stream = await navigator.mediaDevices.getUserMedia({
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true,
          sampleRate: 44100,
        },
      });
      stream.getTracks().forEach((track) => track.stop());
      return true;
    } catch (error) {
      console.warn("Android microphone permission denied:", error);
      return false;
    }
  }

  // For iOS, use the standard Web API
  try {
    const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
    stream.getTracks().forEach((track) => track.stop());
    return true;
  } catch (error) {
    console.warn("iOS microphone permission denied:", error);
    return false;
  }
};

// Check microphone permission status
export const checkMicrophonePermission = async (): Promise<
  "granted" | "denied" | "prompt" | "unavailable"
> => {
  if (!isNativePlatform()) {
    // For web platforms
    try {
      if ("permissions" in navigator) {
        const permission = await navigator.permissions.query({
          name: "microphone" as PermissionName,
        });
        return permission.state as "granted" | "denied" | "prompt";
      } else {
        // Fallback: try to access media devices directly
        try {
          const stream = await navigator.mediaDevices.getUserMedia({
            audio: true,
          });
          stream.getTracks().forEach((track) => track.stop());
          return "granted";
        } catch (error) {
          return (error as Error).name === "NotAllowedError"
            ? "denied"
            : "prompt";
        }
      }
    } catch {
      return "unavailable";
    }
  }

  // For native platforms, try to access the microphone
  try {
    const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
    stream.getTracks().forEach((track) => track.stop());
    return "granted";
  } catch (error) {
    const err = error as Error;
    if (err.name === "NotAllowedError") {
      return "denied";
    } else if (err.name === "NotFoundError") {
      return "unavailable";
    } else {
      return "prompt";
    }
  }
};
