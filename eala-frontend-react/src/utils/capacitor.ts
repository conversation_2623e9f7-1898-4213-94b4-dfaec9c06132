import { Capacitor } from "@capacitor/core";

// Platform detection
export const isNativePlatform = (): boolean => {
  return Capacitor.isNativePlatform();
};

export const getPlatform = (): string => {
  return Capacitor.getPlatform();
};

export const isIOS = (): boolean => {
  return Capacitor.getPlatform() === "ios";
};

export const isAndroid = (): boolean => {
  return Capacitor.getPlatform() === "android";
};

export const isWeb = (): boolean => {
  return Capacitor.getPlatform() === "web";
};

// Safe area utilities
export const getSafeAreaInsets = () => {
  if (!isNativePlatform()) {
    return { top: 0, bottom: 0, left: 0, right: 0 };
  }

  // These will be handled by CSS env() variables in most cases
  // This is a fallback for programmatic usage
  return {
    top: 44, // Typical iOS status bar + notch
    bottom: isIOS() ? 34 : 0, // iOS home indicator
    left: 0,
    right: 0,
  };
};

// Network status
export const isOnline = (): boolean => {
  if (typeof navigator !== "undefined") {
    return navigator.onLine;
  }
  return true;
};

// Device info
export const getDeviceInfo = async () => {
  if (!isNativePlatform()) {
    return {
      model: "Web Browser",
      platform: "web",
      operatingSystem: navigator.userAgent,
      osVersion: "unknown",
      manufacturer: "unknown",
      isVirtual: false,
      webViewVersion: "N/A",
    };
  }

  try {
    const { Device } = await import("@capacitor/device");
    return await Device.getInfo();
  } catch (error) {
    console.warn("Failed to get device info:", error);
    return null;
  }
};

// Haptic feedback (mobile only)
export const hapticImpact = async (
  style: "light" | "medium" | "heavy" = "light"
) => {
  if (!isNativePlatform()) return;

  try {
    const { Haptics, ImpactStyle } = await import("@capacitor/haptics");
    await Haptics.impact({
      style:
        style === "light"
          ? ImpactStyle.Light
          : style === "medium"
          ? ImpactStyle.Medium
          : ImpactStyle.Heavy,
    });
  } catch (error) {
    console.warn("Haptic feedback not available:", error);
  }
};

// Status bar management
export const setStatusBarStyle = async (style: "dark" | "light" = "dark") => {
  if (!isNativePlatform()) return;

  try {
    const { StatusBar, Style } = await import("@capacitor/status-bar");
    await StatusBar.setStyle({
      style: style === "dark" ? Style.Dark : Style.Light,
    });
  } catch (error) {
    console.warn("Status bar control not available:", error);
  }
};

// Keyboard management
export const hideKeyboard = async () => {
  if (!isNativePlatform()) return;

  try {
    const { Keyboard } = await import("@capacitor/keyboard");
    await Keyboard.hide();
  } catch (error) {
    console.warn("Keyboard control not available:", error);
  }
};

// App state management
export const addAppStateChangeListener = (
  callback: (isActive: boolean) => void
) => {
  if (!isNativePlatform()) return () => {};

  let removeListener: (() => void) | null = null;

  import("@capacitor/app")
    .then(({ App }) => {
      App.addListener("appStateChange", ({ isActive }) => {
        callback(isActive);
      }).then((listener) => {
        removeListener = listener.remove;
      });
    })
    .catch((error) => {
      console.warn("App state listener not available:", error);
    });

  return () => {
    if (removeListener) {
      removeListener();
    }
  };
};

// Deep linking
export const addDeepLinkListener = (callback: (url: string) => void) => {
  if (!isNativePlatform()) return () => {};

  let removeListener: (() => void) | null = null;

  import("@capacitor/app")
    .then(({ App }) => {
      App.addListener("appUrlOpen", ({ url }) => {
        callback(url);
      }).then((listener) => {
        removeListener = listener.remove;
      });
    })
    .catch((error) => {
      console.warn("Deep link listener not available:", error);
    });

  return () => {
    if (removeListener) {
      removeListener();
    }
  };
};

// Share functionality
export const shareContent = async (
  title: string,
  text: string,
  url?: string
) => {
  if (!isNativePlatform()) {
    // Fallback to Web Share API
    if (navigator.share) {
      try {
        await navigator.share({ title, text, url });
        return true;
      } catch (error) {
        console.warn("Web share failed:", error);
        return false;
      }
    }
    return false;
  }

  try {
    const { Share } = await import("@capacitor/share");
    await Share.share({ title, text, url });
    return true;
  } catch (error) {
    console.warn("Native share not available:", error);
    return false;
  }
};

// Microphone permission handling using capacitor-voice-recorder
export const requestMicrophonePermission = async (): Promise<boolean> => {
  if (!isNativePlatform()) {
    // For web, use the standard Web API
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      stream.getTracks().forEach((track) => track.stop());
      return true;
    } catch (error) {
      console.warn("Web microphone permission denied:", error);
      return false;
    }
  }

  // For native platforms, use the voice recorder plugin
  try {
    const { VoiceRecorder } = await import("capacitor-voice-recorder");

    // Request permission through the plugin
    const permission = await VoiceRecorder.requestAudioRecordingPermission();

    if (permission.value) {
      console.log("Microphone permission granted via VoiceRecorder plugin");
      return true;
    } else {
      console.warn("Microphone permission denied via VoiceRecorder plugin");
      return false;
    }
  } catch (error) {
    console.warn(
      "Failed to request microphone permission via VoiceRecorder plugin:",
      error
    );

    // Fallback to web API for native platforms
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      stream.getTracks().forEach((track) => track.stop());
      return true;
    } catch (fallbackError) {
      console.warn(
        "Fallback microphone permission also failed:",
        fallbackError
      );
      return false;
    }
  }
};

// Location permission handling
export const requestLocationPermission = async (): Promise<boolean> => {
  if (!isNativePlatform()) {
    // For web, use the Geolocation API
    try {
      await new Promise<GeolocationPosition>((resolve, reject) => {
        navigator.geolocation.getCurrentPosition(resolve, reject, {
          timeout: 10000,
          enableHighAccuracy: false,
        });
      });
      return true;
    } catch (error) {
      console.warn("Web location permission denied:", error);
      return false;
    }
  }

  // For native platforms, try to access location
  try {
    await new Promise<GeolocationPosition>((resolve, reject) => {
      navigator.geolocation.getCurrentPosition(resolve, reject, {
        timeout: 10000,
        enableHighAccuracy: true,
        maximumAge: 300000, // 5 minutes
      });
    });
    return true;
  } catch (error) {
    console.warn("Native location permission denied:", error);
    return false;
  }
};

// Check microphone permission status using capacitor-voice-recorder
export const checkMicrophonePermission = async (): Promise<
  "granted" | "denied" | "prompt" | "unavailable"
> => {
  if (!isNativePlatform()) {
    // For web platforms
    try {
      if ("permissions" in navigator) {
        const permission = await navigator.permissions.query({
          name: "microphone" as PermissionName,
        });
        return permission.state as "granted" | "denied" | "prompt";
      } else {
        // Fallback: try to access media devices directly
        try {
          const stream = await navigator.mediaDevices.getUserMedia({
            audio: true,
          });
          stream.getTracks().forEach((track: MediaStreamTrack) => track.stop());
          return "granted";
        } catch (error) {
          return (error as Error).name === "NotAllowedError"
            ? "denied"
            : "prompt";
        }
      }
    } catch {
      return "unavailable";
    }
  }

  // For native platforms, use the voice recorder plugin
  try {
    const { VoiceRecorder } = await import("capacitor-voice-recorder");

    // Check if the plugin has permission
    const hasPermission = await VoiceRecorder.hasAudioRecordingPermission();

    if (hasPermission.value) {
      return "granted";
    } else {
      // If no permission, we need to determine if it's denied or just not requested
      // Try to request permission to see the current state
      try {
        const permission =
          await VoiceRecorder.requestAudioRecordingPermission();
        return permission.value ? "granted" : "denied";
      } catch {
        return "prompt";
      }
    }
  } catch (error) {
    console.warn(
      "Failed to check microphone permission via VoiceRecorder plugin:",
      error
    );

    // Fallback to web API
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      stream.getTracks().forEach((track: MediaStreamTrack) => track.stop());
      return "granted";
    } catch (fallbackError) {
      const err = fallbackError as Error;
      if (err.name === "NotAllowedError") {
        return "denied";
      } else if (err.name === "NotFoundError") {
        return "unavailable";
      } else {
        return "prompt";
      }
    }
  }
};

// Check location permission status
export const checkLocationPermission = async (): Promise<
  "granted" | "denied" | "prompt" | "unavailable"
> => {
  if (!isNativePlatform()) {
    // For web platforms
    try {
      if ("permissions" in navigator) {
        const permission = await navigator.permissions.query({
          name: "geolocation" as PermissionName,
        });
        return permission.state as "granted" | "denied" | "prompt";
      } else {
        // Fallback: try to access geolocation directly
        try {
          await new Promise<GeolocationPosition>((resolve, reject) => {
            navigator.geolocation.getCurrentPosition(resolve, reject, {
              timeout: 5000,
              enableHighAccuracy: false,
            });
          });
          return "granted";
        } catch (error) {
          const err = error as GeolocationPositionError;
          return err.code === err.PERMISSION_DENIED ? "denied" : "prompt";
        }
      }
    } catch {
      return "unavailable";
    }
  }

  // For native platforms, try to access location
  try {
    await new Promise<GeolocationPosition>((resolve, reject) => {
      navigator.geolocation.getCurrentPosition(resolve, reject, {
        timeout: 5000,
        enableHighAccuracy: false,
      });
    });
    return "granted";
  } catch (error) {
    const err = error as GeolocationPositionError;
    if (err.code === err.PERMISSION_DENIED) {
      return "denied";
    } else if (err.code === err.POSITION_UNAVAILABLE) {
      return "unavailable";
    } else {
      return "prompt";
    }
  }
};

// Request multiple permissions at once
export const requestPermissions = async (
  permissions: ("microphone" | "location")[]
): Promise<{
  microphone?: boolean;
  location?: boolean;
}> => {
  const results: { microphone?: boolean; location?: boolean } = {};

  for (const permission of permissions) {
    try {
      if (permission === "microphone") {
        results.microphone = await requestMicrophonePermission();
      } else if (permission === "location") {
        results.location = await requestLocationPermission();
      }
    } catch (error) {
      console.warn(`Failed to request ${permission} permission:`, error);
      if (permission === "microphone") {
        results.microphone = false;
      } else if (permission === "location") {
        results.location = false;
      }
    }
  }

  return results;
};

// Get current location with permission handling
export const getCurrentLocation = async (options?: {
  enableHighAccuracy?: boolean;
  timeout?: number;
  maximumAge?: number;
}): Promise<GeolocationPosition | null> => {
  try {
    // Check permission first
    const permissionStatus = await checkLocationPermission();

    if (permissionStatus === "denied" || permissionStatus === "unavailable") {
      console.warn("Location permission not available");
      return null;
    }

    // If permission is prompt, try to request it
    if (permissionStatus === "prompt") {
      const granted = await requestLocationPermission();
      if (!granted) {
        console.warn("Location permission denied by user");
        return null;
      }
    }

    // Get current position
    return new Promise<GeolocationPosition>((resolve, reject) => {
      navigator.geolocation.getCurrentPosition(resolve, reject, {
        enableHighAccuracy: options?.enableHighAccuracy ?? true,
        timeout: options?.timeout ?? 15000,
        maximumAge: options?.maximumAge ?? 300000, // 5 minutes
      });
    });
  } catch (error) {
    console.warn("Failed to get current location:", error);
    return null;
  }
};

// Watch location with permission handling
export const watchLocation = async (
  callback: (position: GeolocationPosition) => void,
  errorCallback?: (error: GeolocationPositionError) => void,
  options?: {
    enableHighAccuracy?: boolean;
    timeout?: number;
    maximumAge?: number;
  }
): Promise<number | null> => {
  try {
    // Check permission first
    const permissionStatus = await checkLocationPermission();

    if (permissionStatus === "denied" || permissionStatus === "unavailable") {
      console.warn("Location permission not available");
      return null;
    }

    // If permission is prompt, try to request it
    if (permissionStatus === "prompt") {
      const granted = await requestLocationPermission();
      if (!granted) {
        console.warn("Location permission denied by user");
        return null;
      }
    }

    // Watch position
    return navigator.geolocation.watchPosition(callback, errorCallback, {
      enableHighAccuracy: options?.enableHighAccuracy ?? true,
      timeout: options?.timeout ?? 15000,
      maximumAge: options?.maximumAge ?? 300000, // 5 minutes
    });
  } catch (error) {
    console.warn("Failed to watch location:", error);
    return null;
  }
};
