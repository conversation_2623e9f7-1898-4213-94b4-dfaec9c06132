import React, { useState } from 'react';
import { AudioQualityResult } from '../../types/pronunciation';
import AudioRecorder from './AudioRecorder';
import { VolumeUpIcon } from './Icons';

interface PronunciationRecorderProps {
  sentence: string;
  onAudioRecorded: (audioBlob: Blob, qualityResult?: AudioQualityResult) => void;
  onError: (error: string) => void;
}

const PronunciationRecorder: React.FC<PronunciationRecorderProps> = ({
  sentence,
  onAudioRecorded,
  onError
}) => {
  const [, setHasStartedRecording] = useState(false);

  const handleAudioRecorded = (audioBlob: Blob) => {
    // Basic audio quality validation
    const qualityResult: AudioQualityResult = {
      isValid: audioBlob.size > 1000, // At least 1KB
      duration: 0, // We don't have duration info here
      hasAudioContent: audioBlob.size > 1000,
      volumeLevel: 'good', // Assume good for now
      errors: audioBlob.size <= 1000 ? ['Audio too short'] : [],
      warnings: []
    };

    onAudioRecorded(audioBlob, qualityResult);
  };

  const handleError = (error: string) => {
    setHasStartedRecording(false);
    onError(error);
  };


  return (
    <div className="course-card">
      <div className="text-center mb-8">
        <h3 className="heading-secondary mb-4">
          Practice This Sentence
        </h3>
        
        {/* Sentence Display */}
        <div className="bg-secondary-yellow/20 border border-secondary-yellow rounded-lg p-6 mb-6">
          <p className="text-xl font-bold text-neutral-text leading-relaxed">
            "{sentence}"
          </p>
        </div>

        {/* Instructions */}
        <div className="text-left mb-6 space-y-3">
          <div className="flex items-start text-sm body-text">
            <span className="bg-primary-blue text-white font-bold rounded-full w-6 h-6 flex items-center justify-center text-xs mr-3 mt-0.5 flex-shrink-0">
              1
            </span>
            <span>Read the sentence above carefully</span>
          </div>
          <div className="flex items-start text-sm body-text">
            <span className="bg-primary-blue text-white font-bold rounded-full w-6 h-6 flex items-center justify-center text-xs mr-3 mt-0.5 flex-shrink-0">
              2
            </span>
            <span>Tap the microphone button and speak clearly</span>
          </div>
          <div className="flex items-start text-sm body-text">
            <span className="bg-primary-blue text-white font-bold rounded-full w-6 h-6 flex items-center justify-center text-xs mr-3 mt-0.5 flex-shrink-0">
              3
            </span>
            <span>Get instant feedback on your pronunciation</span>
          </div>
        </div>
      </div>

      {/* Audio Recorder */}
      <div className="flex justify-center mb-6">
        <AudioRecorder
          onAudioRecorded={handleAudioRecorded}
          onError={handleError}
          maxDuration={30} // 30 seconds for pronunciation practice
        />
      </div>

      {/* Tips */}
      <div className="bg-primary-blue/10 border border-primary-blue/20 rounded-lg p-4">
        <div className="flex items-start">
          <div className="icon-container language mr-3 mt-0.5 flex-shrink-0 w-8 h-8">
            <VolumeUpIcon className="w-4 h-4" />
          </div>
          <div>
            <h4 className="text-primary-blue font-semibold mb-2">Recording Tips:</h4>
            <ul className="text-sm text-primary-blue/80 space-y-1">
              <li>• Speak clearly and at a normal pace</li>
              <li>• Make sure you're in a quiet environment</li>
              <li>• Hold your device about 6 inches from your mouth</li>
              <li>• Say the entire sentence in one recording</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PronunciationRecorder;