import React, { useState, useRef, useEffect, useCallback } from 'react';
import { AudioRecorderState, MicrophonePermissionState, PermissionError } from '../../types/conversation';
import { MicrophoneIcon, VolumeOffIcon, LoadingSpinner, ExclamationTriangleIcon, CheckCircleIcon } from './Icons';

interface AudioRecorderProps {
  onAudioRecorded: (audioBlob: Blob) => void;
  onError: (error: string) => void;
  onPermissionChange?: (state: MicrophonePermissionState) => void;
  disabled?: boolean;
  maxDuration?: number;
}

const AudioRecorder: React.FC<AudioRecorderProps> = ({
  onAudioRecorded,
  onError,
  onPermissionChange,
  disabled = false,
  maxDuration = 60
}) => {
  const [recorderState, setRecorderState] = useState<AudioRecorderState>({
    state: 'idle',
    duration: 0,
    permissionState: 'checking'
  });
  
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const audioChunksRef = useRef<Blob[]>([]);
  const streamRef = useRef<MediaStream | null>(null);
  const timerRef = useRef<NodeJS.Timeout | null>(null);
  const startTimeRef = useRef<number>(0);

  // Check browser support
  const isSupported = typeof navigator !== 'undefined' &&
                     navigator.mediaDevices &&
                     typeof navigator.mediaDevices.getUserMedia === 'function' &&
                     typeof MediaRecorder !== 'undefined';

  // Permission handling functions
  const createPermissionError = (error: Error): PermissionError => {
    switch (error.name) {
      case 'NotAllowedError':
        return {
          type: 'NotAllowedError',
          message: 'Microphone access denied',
          userGuidance: 'Please allow microphone access to use voice recording.',
          browserSpecific: {
            chrome: 'Click the microphone icon in the address bar and select "Allow"',
            firefox: 'Click the microphone icon in the address bar and select "Allow"',
            safari: 'Go to Safari > Settings > Websites > Microphone and allow access',
            edge: 'Click the microphone icon in the address bar and select "Allow"'
          }
        };
      case 'NotFoundError':
        return {
          type: 'NotFoundError',
          message: 'No microphone found',
          userGuidance: 'Please connect a microphone and try again.',
        };
      case 'NotSupportedError':
        return {
          type: 'NotSupportedError',
          message: 'Audio recording not supported',
          userGuidance: 'Your browser does not support audio recording. Please use a modern browser.',
        };
      case 'AbortError':
        return {
          type: 'AbortError',
          message: 'Recording was interrupted',
          userGuidance: 'The recording was stopped unexpectedly. Please try again.',
        };
      default:
        return {
          type: 'UnknownError',
          message: error.message || 'Unknown error occurred',
          userGuidance: 'An unexpected error occurred. Please refresh the page and try again.',
        };
    }
  };

  // Check microphone permission status
  const checkPermissionStatus = useCallback(async (): Promise<MicrophonePermissionState> => {
    if (!isSupported) {
      return 'unavailable';
    }

    try {
      if ('permissions' in navigator) {
        const permission = await navigator.permissions.query({ name: 'microphone' as PermissionName });
        return permission.state as MicrophonePermissionState;
      } else {
        // Fallback: try to access media devices directly
        try {
          const nav = navigator as Navigator;
          if (nav.mediaDevices) {
            const stream = await nav.mediaDevices.getUserMedia({ audio: true });
            stream.getTracks().forEach((track: MediaStreamTrack) => track.stop());
            return 'granted';
          } else {
            return 'unavailable';
          }
        } catch (error) {
          return error instanceof Error && error.name === 'NotAllowedError' ? 'denied' : 'prompt';
        }
      }
    } catch {
      return 'unavailable';
    }
  }, [isSupported]);

  // Request microphone permission
  const requestPermission = useCallback(async (): Promise<boolean> => {
    try {
      setRecorderState(prev => ({
        ...prev,
        permissionState: 'checking',
        permissionError: undefined
      }));

      const stream = await navigator.mediaDevices.getUserMedia({
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true,
          sampleRate: 44100
        }
      });

      streamRef.current = stream;

      setRecorderState(prev => ({
        ...prev,
        permissionState: 'granted',
        permissionError: undefined
      }));

      onPermissionChange?.('granted');
      return true;

    } catch (error) {
      const permissionError = createPermissionError(error as Error);

      setRecorderState(prev => ({
        ...prev,
        permissionState: 'denied',
        permissionError: permissionError.userGuidance
      }));

      onPermissionChange?.('denied');
      onError(permissionError.userGuidance);
      return false;
    }
  }, [onError, onPermissionChange]);

  // Initialize permission status
  const initializePermissions = useCallback(async () => {
    const status = await checkPermissionStatus();
    
    setRecorderState(prev => ({
      ...prev,
      permissionState: status
    }));

    onPermissionChange?.(status);

    if (status === 'granted') {
      try {
        const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
        streamRef.current = stream;
      } catch {
        setRecorderState(prev => ({
          ...prev,
          permissionState: 'denied'
        }));
        onPermissionChange?.('denied');
      }
    }
  }, [checkPermissionStatus, onPermissionChange]);

  // Clean up resources
  const cleanup = useCallback(() => {
    if (timerRef.current) {
      clearInterval(timerRef.current);
      timerRef.current = null;
    }

    if (streamRef.current) {
      streamRef.current.getTracks().forEach(track => track.stop());
      streamRef.current = null;
    }

    if (mediaRecorderRef.current && mediaRecorderRef.current.state !== 'inactive') {
      mediaRecorderRef.current.stop();
    }

    audioChunksRef.current = [];
  }, []);

  // Start recording
  const startRecording = async () => {
    if (!isSupported) {
      onError('Audio recording is not supported in this browser');
      return;
    }

    if (recorderState.permissionState !== 'granted') {
      const permissionGranted = await requestPermission();
      if (!permissionGranted) return;
    }

    if (!streamRef.current) {
      const permissionGranted = await requestPermission();
      if (!permissionGranted) return;
    }

    try {
      setRecorderState(prev => ({
        ...prev,
        state: 'recording',
        duration: 0,
        error: undefined
      }));

      audioChunksRef.current = [];
      const stream = streamRef.current;

      if (!stream) {
        throw new Error('No media stream available');
      }
      
      const mimeType = MediaRecorder.isTypeSupported('audio/webm') ? 'audio/webm' : 'audio/mp4';
      const mediaRecorder = new MediaRecorder(stream, { mimeType });
      mediaRecorderRef.current = mediaRecorder;
      
      mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          audioChunksRef.current.push(event.data);
        }
      };
      
      mediaRecorder.onstop = () => {
        const audioBlob = new Blob(audioChunksRef.current, {
          type: mediaRecorder.mimeType
        });

        setRecorderState(prev => ({
          ...prev,
          state: 'idle',
          audioBlob,
          duration: 0
        }));

        onAudioRecorded(audioBlob);
        cleanup();
      };
      
      mediaRecorder.onerror = () => {
        setRecorderState(prev => ({ 
          ...prev, 
          state: 'error', 
          error: 'Recording failed' 
        }));
        onError('Recording failed');
        cleanup();
      };
      
      mediaRecorder.start(100);
      startTimeRef.current = Date.now();
      
      timerRef.current = setInterval(() => {
        const elapsed = Math.floor((Date.now() - startTimeRef.current) / 1000);
        setRecorderState(prev => ({ ...prev, duration: elapsed }));
        
        if (elapsed >= maxDuration) {
          stopRecording();
        }
      }, 100);
      
    } catch (error) {
      const permissionError = createPermissionError(error as Error);
      setRecorderState(prev => ({
        ...prev,
        state: 'error',
        error: permissionError.message,
        permissionState: 'denied',
        permissionError: permissionError.userGuidance
      }));
      onError(permissionError.userGuidance);
      onPermissionChange?.('denied');
      cleanup();
    }
  };

  // Stop recording
  const stopRecording = () => {
    if (mediaRecorderRef.current?.state === 'recording') {
      mediaRecorderRef.current.stop();
    } else if (recorderState.state === 'recording') {
      setRecorderState(prev => ({ ...prev, state: 'idle', duration: 0 }));
      cleanup();
    }
  };

  // Format duration for display
  const formatDuration = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  // Initialize permissions on mount
  useEffect(() => {
    initializePermissions();
  }, [initializePermissions]);

  // Cleanup on unmount
  useEffect(() => {
    return cleanup;
  }, [cleanup]);

  // Render recording button
  const renderRecordButton = () => {
    const isRecording = recorderState.state === 'recording';
    const isProcessing = recorderState.state === 'processing';
    const hasError = recorderState.state === 'error';
    const isCheckingPermission = recorderState.permissionState === 'checking';
    const permissionDenied = recorderState.permissionState === 'denied';
    const permissionUnavailable = recorderState.permissionState === 'unavailable';

    const isDisabled = disabled || isProcessing || !isSupported || isCheckingPermission || permissionUnavailable;

    let buttonColor = 'bg-blue-400';
    let buttonText = 'Tap to record';
    let buttonIcon = MicrophoneIcon;

    if (isRecording) {
      buttonColor = 'bg-red-400 animate-pulse';
      buttonText = 'Recording... tap to stop';
      buttonIcon = () => <div className="w-6 h-6 bg-white rounded-sm" />;
    } else if (hasError || permissionDenied) {
      buttonColor = 'bg-red-300';
      buttonText = 'Permission required';
      buttonIcon = VolumeOffIcon;
    } else if (isCheckingPermission) {
      buttonColor = 'bg-yellow-300';
      buttonText = 'Checking...';
      buttonIcon = LoadingSpinner;
    } else if (permissionUnavailable) {
      buttonColor = 'bg-gray-300';
      buttonText = 'Unavailable';
      buttonIcon = VolumeOffIcon;
    } else if (recorderState.permissionState === 'prompt') {
      buttonColor = 'bg-blue-400';
      buttonText = 'Allow microphone';
      buttonIcon = MicrophoneIcon;
    }

    const Icon = buttonIcon;

    return (
      <div className="flex flex-col items-center space-y-3">
        <button
          onClick={isRecording ? stopRecording : startRecording}
          disabled={isDisabled}
          className={`
            relative flex items-center justify-center w-20 h-20 rounded-full transition-all duration-150
            border-2 border-black
            ${buttonColor}
            ${isDisabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer shadow-brutal-lg hover:shadow-none hover:translate-x-1 hover:translate-y-1 active:translate-x-1 active:translate-y-1'}
            focus:outline-none focus:ring-4 focus:ring-blue-300
          `}
          aria-label={buttonText}
        >
          <Icon className="w-8 h-8 text-white" />
        </button>
        
        <p className="text-sm text-gray-600 text-center max-w-xs">
          {buttonText}
        </p>
      </div>
    );
  };

  // Render duration display
  const renderDuration = () => {
    if (recorderState.state !== 'recording' || recorderState.duration === 0) {
      return null;
    }
    
    return (
      <div className="text-center">
        <div className="text-lg font-mono text-gray-900 mb-1">
          {formatDuration(recorderState.duration)}
        </div>
        <div className="text-xs text-gray-500">
          / {formatDuration(maxDuration)}
        </div>
      </div>
    );
  };

  // Render permission status
  const renderPermissionStatus = () => {
    const { permissionState } = recorderState;

    switch (permissionState) {
      case 'checking':
        return (
          <div className="flex items-center justify-center text-yellow-600 text-sm">
            <LoadingSpinner className="w-4 h-4 mr-2" />
            <span>Checking microphone access...</span>
          </div>
        );
      case 'granted':
        return (
          <div className="flex items-center justify-center text-green-600 text-sm">
            <CheckCircleIcon className="w-4 h-4 mr-2" />
            <span>Microphone ready</span>
          </div>
        );
      case 'denied':
        return (
          <div className="flex items-center justify-center text-red-600 text-sm">
            <ExclamationTriangleIcon className="w-4 h-4 mr-2" />
            <span>Microphone access denied</span>
          </div>
        );
      case 'unavailable':
        return (
          <div className="flex items-center justify-center text-gray-500 text-sm">
            <VolumeOffIcon className="w-4 h-4 mr-2" />
            <span>Microphone not available</span>
          </div>
        );
      default:
        return null;
    }
  };

  if (!isSupported) {
    return (
      <div className="text-center py-8">
        <VolumeOffIcon className="w-12 h-12 text-gray-400 mx-auto mb-4" />
        <p className="text-gray-500 mb-2">Audio recording not supported</p>
        <p className="text-sm text-gray-400">
          Please use a modern browser
        </p>
      </div>
    );
  }

  return (
    <div className="flex flex-col items-center space-y-4">
      {renderRecordButton()}
      {renderDuration()}
      {renderPermissionStatus()}
    </div>
  );
};

export default AudioRecorder;