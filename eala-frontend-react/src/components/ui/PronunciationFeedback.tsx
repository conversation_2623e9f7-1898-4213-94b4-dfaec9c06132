import React from 'react';
import { PronunciationAssessmentResponse } from '../../types/pronunciation';
// import { CheckCircleIcon, ExclamationTriangleIcon } from './Icons';

interface PronunciationFeedbackProps {
  assessment: PronunciationAssessmentResponse;
  referenceText: string;
  onTryAgain: () => void;
  onNewSentence: () => void;
}

const PronunciationFeedback: React.FC<PronunciationFeedbackProps> = ({
  assessment,
  referenceText,
  onTryAgain,
  onNewSentence
}) => {
  const getScoreColor = (score?: number): string => {
    if (!score) return 'text-gray-500';
    if (score >= 80) return 'text-green-600';
    if (score >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getScoreBgColor = (score?: number): string => {
    if (!score) return 'bg-gray-200';
    if (score >= 80) return 'bg-green-200';
    if (score >= 60) return 'bg-yellow-200';
    return 'bg-red-200';
  };

  const getOverallFeedback = (score?: number): { message: string; emoji: string } => {
    if (!score) return { message: 'No score available', emoji: '❓' };
    if (score >= 80) return { message: 'Excellent pronunciation!', emoji: '🎉' };
    if (score >= 60) return { message: 'Good job! Keep practicing.', emoji: '👍' };
    return { message: 'Keep practicing to improve.', emoji: '💪' };
  };

  const scores = assessment.scores;
  const overallScore = scores?.overall_score;
  const feedback = getOverallFeedback(overallScore);

  return (
    <div className="space-y-6">
      {/* Overall Result */}
      <div className="course-card text-center">
        <div className="text-4xl mb-4">{feedback.emoji}</div>
        <h3 className="heading-secondary mb-2">
          {feedback.message}
        </h3>
        {overallScore && (
          <div className={`text-6xl font-bold mb-4 ${getScoreColor(overallScore)}`}>
            {Math.round(overallScore)}
          </div>
        )}
        <p className="body-text mb-6">
          Overall Pronunciation Score
        </p>
      </div>

      {/* Detailed Scores */}
      {scores && (
        <div className="course-card">
          <h4 className="heading-secondary mb-4">
            Detailed Breakdown
          </h4>
          <div className="grid grid-cols-2 sm:grid-cols-4 gap-4">
            {[
              { label: 'Accuracy', score: scores.accuracy_score, description: 'Correct pronunciation of sounds' },
              { label: 'Fluency', score: scores.fluency_score, description: 'Natural rhythm and flow' },
              { label: 'Completeness', score: scores.completeness_score, description: 'All words pronounced' },
              { label: 'Prosody', score: scores.prosody_score, description: 'Stress and intonation' }
            ].map((item) => (
              <div
                key={item.label}
                className={`${getScoreBgColor(item.score)} border border-neutral-medium-gray rounded-lg p-4 text-center`}
              >
                <div className={`text-2xl font-bold mb-1 ${getScoreColor(item.score)}`}>
                  {item.score ? Math.round(item.score) : '--'}
                </div>
                <div className="text-sm font-medium text-neutral-text mb-1">
                  {item.label}
                </div>
                <div className="text-xs text-neutral-dark-gray">
                  {item.description}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Word-level Feedback */}
      {assessment.word_level_feedback && assessment.word_level_feedback.length > 0 && (
        <div className="course-card">
          <h4 className="heading-secondary mb-4">
            Word-by-Word Analysis
          </h4>
          <div className="bg-neutral-light-gray border border-neutral-medium-gray rounded-lg p-4">
            <div className="flex flex-wrap gap-2">
              {assessment.word_level_feedback.map((word, index) => (
                <span
                  key={index}
                  className={`
                    px-3 py-1 rounded-lg border border-neutral-medium-gray text-sm font-medium
                    ${getScoreBgColor(word.accuracy_score)}
                    ${getScoreColor(word.accuracy_score)}
                  `}
                  title={`${word.word}: ${word.accuracy_score ? Math.round(word.accuracy_score) : 'No score'}/100`}
                >
                  {word.word}
                  {word.accuracy_score && (
                    <span className="ml-1 text-xs">
                      ({Math.round(word.accuracy_score)})
                    </span>
                  )}
                </span>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* AI Feedback */}
      {assessment.overall_feedback && (
        <div className="course-card">
          <h4 className="heading-secondary mb-4">
            AI Feedback
          </h4>
          <div className="bg-primary-blue/10 border border-primary-blue/20 rounded-lg p-4">
            <p className="text-primary-blue/90">{assessment.overall_feedback}</p>
          </div>
        </div>
      )}

      {/* Original Sentence */}
      <div className="course-card">
        <h4 className="heading-secondary mb-4">
          Reference Sentence
        </h4>
        <div className="bg-secondary-yellow/20 border border-secondary-yellow rounded-lg p-4">
          <p className="text-neutral-text font-medium">"{referenceText}"</p>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex flex-col sm:flex-row gap-4">
        <button
          onClick={onTryAgain}
          className="flex-1 btn-primary"
        >
          Try This Sentence Again
        </button>
        <button
          onClick={onNewSentence}
          className="flex-1 btn-primary bg-accent-green hover:bg-accent-green/90"
        >
          Try a New Sentence
        </button>
      </div>

      {/* Processing Time */}
      {assessment.processing_time_ms && (
        <div className="text-center text-xs text-gray-500">
          Analysis completed in {Math.round(assessment.processing_time_ms)}ms
        </div>
      )}
    </div>
  );
};

export default PronunciationFeedback;