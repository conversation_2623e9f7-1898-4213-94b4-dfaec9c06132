import React, { useState, useCallback } from 'react';
import { useMutation } from '@tanstack/react-query';
import { PronunciationPracticeState, AudioQualityResult } from '../../types/pronunciation';
import { generatePracticeSentence, assessPronunciation } from '../../services/api';
import { <PERSON><PERSON><PERSON><PERSON>ner, ExclamationTriangleIcon } from '../ui/Icons';
import PronunciationRecorder from '../ui/PronunciationRecorder';
import PronunciationFeedback from '../ui/PronunciationFeedback';

const PronunciationPractice: React.FC = () => {
  const [practiceState, setPracticeState] = useState<PronunciationPracticeState>({
    currentStep: 'generate',
    difficulty: 'intermediate',
    isLoading: false
  });

  // Generate sentence mutation
  const generateMutation = useMutation({
    mutationFn: generatePracticeSentence,
    onSuccess: (response) => {
      setPracticeState(prev => ({
        ...prev,
        sentence: response.sentence,
        currentStep: 'display',
        isLoading: false,
        error: undefined
      }));
    },
    onError: (error) => {
      setPracticeState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Failed to generate sentence',
        isLoading: false
      }));
    }
  });

  // Assessment mutation
  const assessmentMutation = useMutation({
    mutationFn: ({ audioBlob, referenceText }: { audioBlob: Blob; referenceText: string }) =>
      assessPronunciation(audioBlob, referenceText, 'default_user'),
    onSuccess: (assessment) => {
      setPracticeState(prev => ({
        ...prev,
        assessment,
        currentStep: 'feedback',
        isLoading: false
      }));
    },
    onError: (error) => {
      setPracticeState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Assessment failed',
        currentStep: 'display',
        isLoading: false
      }));
    }
  });

  // Generate a new practice sentence
  const generateSentence = useCallback(() => {
    setPracticeState(prev => ({ ...prev, isLoading: true, error: undefined }));
    generateMutation.mutate({
      difficulty: practiceState.difficulty
    });
  }, [practiceState.difficulty, generateMutation]);

  // Handle difficulty change
  const handleDifficultyChange = useCallback((difficulty: 'beginner' | 'intermediate' | 'advanced') => {
    setPracticeState(prev => ({
      ...prev,
      difficulty,
      sentence: undefined,
      currentStep: 'generate',
      error: undefined
    }));
  }, []);

  // Handle audio recording completion
  const handleAudioRecorded = useCallback(async (audioBlob: Blob, qualityResult?: AudioQualityResult) => {
    if (!practiceState.sentence) {
      setPracticeState(prev => ({
        ...prev,
        error: 'No sentence available for assessment',
        currentStep: 'display'
      }));
      return;
    }

    setPracticeState(prev => ({
      ...prev,
      audioBlob,
      currentStep: 'assess',
      isLoading: true,
      error: undefined
    }));

    console.log('Starting pronunciation assessment:', {
      sentence: practiceState.sentence,
      audioSize: audioBlob.size,
      quality: qualityResult
    });

    assessmentMutation.mutate({
      audioBlob,
      referenceText: practiceState.sentence
    });
  }, [practiceState.sentence, assessmentMutation]);

  // Handle recording errors
  const handleRecordingError = useCallback((error: string) => {
    setPracticeState(prev => ({
      ...prev,
      error,
      currentStep: 'display'
    }));
  }, []);

  // Start over with new sentence
  const startOver = useCallback(() => {
    setPracticeState(prev => ({
      ...prev,
      sentence: undefined,
      audioBlob: undefined,
      assessment: undefined,
      error: undefined,
      currentStep: 'generate'
    }));
  }, []);

  // Try the same sentence again
  const tryAgain = useCallback(() => {
    setPracticeState(prev => ({
      ...prev,
      audioBlob: undefined,
      assessment: undefined,
      error: undefined,
      currentStep: 'display'
    }));
  }, []);

  return (
    <div className="flex-1 overflow-y-auto">
      <div className="p-4 pb-safe-bottom">
        {/* Welcome Section */}
        <div className="learning-card text-center mb-6">
          <h2 className="heading-primary mb-2">
            Pronunciation Practice
          </h2>
          <p className="text-white/90 text-lg">
            Practice your pronunciation with AI-generated sentences
          </p>
          <div className="mt-4 flex justify-center">
            <div className="bg-secondary-yellow w-16 h-16 rounded-full flex items-center justify-center shadow-icon-container">
              <span className="text-2xl">🎤</span>
            </div>
          </div>
        </div>

        {/* Difficulty Selection */}
        <div className="course-card mb-6">
          <h3 className="heading-secondary mb-4">
            Choose Difficulty Level:
          </h3>
          <div className="flex gap-3 justify-center flex-wrap">
            {(['beginner', 'intermediate', 'advanced'] as const).map((level) => (
              <button
                key={level}
                onClick={() => handleDifficultyChange(level)}
                disabled={practiceState.isLoading || generateMutation.isPending}
                className={`px-6 py-3 rounded-lg font-semibold transition-all duration-200 disabled:opacity-50 ${
                  practiceState.difficulty === level
                    ? 'bg-primary-blue text-white shadow-card-medium'
                    : 'bg-neutral-light-gray text-neutral-text hover:bg-neutral-medium-gray'
                }`}
              >
                {level.charAt(0).toUpperCase() + level.slice(1)}
              </button>
            ))}
          </div>
        </div>

        {/* Main Content */}
        <div className="space-y-6">
          {/* Step 1: Generate Sentence */}
          {practiceState.currentStep === 'generate' && (
            <div className="course-card text-center">
              <div className="icon-container chemistry mx-auto mb-4">
                <span className="text-2xl">🎯</span>
              </div>
              <h3 className="heading-secondary mb-4">
                Ready to Practice?
              </h3>
              <p className="body-text mb-6">
                Click the button below to generate a {practiceState.difficulty} level practice sentence.
              </p>
              <button
                onClick={generateSentence}
                disabled={generateMutation.isPending}
                className="btn-primary disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {generateMutation.isPending ? (
                  <div className="flex items-center justify-center">
                    <LoadingSpinner className="w-5 h-5 mr-2" />
                    Generating...
                  </div>
                ) : (
                  'Generate Practice Sentence'
                )}
              </button>
            </div>
          )}

          {/* Step 2: Display Sentence & Record */}
          {practiceState.currentStep === 'display' && practiceState.sentence && (
            <PronunciationRecorder
              sentence={practiceState.sentence}
              onAudioRecorded={handleAudioRecorded}
              onError={handleRecordingError}
            />
          )}

          {/* Step 3: Assessment Processing */}
          {practiceState.currentStep === 'assess' && (
            <div className="course-card text-center">
              <div className="icon-container biology mx-auto mb-4">
                <LoadingSpinner className="w-6 h-6 text-white" />
              </div>
              <h3 className="heading-secondary mb-4">
                Analyzing Your Pronunciation...
              </h3>
              <div className="flex flex-col items-center gap-4">
                <LoadingSpinner className="w-12 h-12 text-primary-blue" />
                <p className="body-text">
                  Please wait while we analyze your pronunciation using AI...
                </p>
              </div>
            </div>
          )}

          {/* Step 4: Feedback Display */}
          {practiceState.currentStep === 'feedback' && practiceState.assessment && practiceState.sentence && (
            <PronunciationFeedback
              assessment={practiceState.assessment}
              referenceText={practiceState.sentence}
              onTryAgain={tryAgain}
              onNewSentence={startOver}
            />
          )}

          {/* Error Display */}
          {practiceState.error && (
            <div className="course-card border-l-4 border-red-500">
              <div className="flex items-start">
                <div className="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mr-4">
                  <ExclamationTriangleIcon className="w-6 h-6 text-red-600" />
                </div>
                <div className="flex-1">
                  <h4 className="text-red-800 font-semibold mb-2">Error:</h4>
                  <p className="text-red-700 text-sm mb-4">{practiceState.error}</p>
                  <button
                    onClick={startOver}
                    className="btn-primary bg-red-500 hover:bg-red-600"
                  >
                    Start Over
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default PronunciationPractice;