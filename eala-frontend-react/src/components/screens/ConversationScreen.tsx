import React, { useState, useEffect, useRef } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useMutation, useQuery } from '@tanstack/react-query';
import { startConversation, respondToConversation, saveAudioForDebug, audioToBase64 } from '../../services/api';
import { ConversationMessage, ConversationRespondRequest, MicrophonePermissionState } from '../../types/conversation';
import { LoadingSpinner, ExclamationTriangleIcon } from '../ui/Icons';
import AudioRecorder from '../ui/AudioRecorder';

const ConversationScreen: React.FC = () => {
  const { topicId } = useParams<{ topicId: string }>();
  const navigate = useNavigate();
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const [messages, setMessages] = useState<ConversationMessage[]>([]);
  const [sessionId, setSessionId] = useState<string | null>(null);
  const [textInput, setTextInput] = useState('');
  const [inputMode, setInputMode] = useState<'text' | 'audio'>('audio');
  const [microphonePermission, setMicrophonePermission] = useState<MicrophonePermissionState>('checking');
  const [error, setError] = useState<string | null>(null);

  // Auto-scroll to bottom when new messages are added
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // Start conversation query
  const {
    data: conversationData,
    isLoading: isInitializing,
    isError: hasInitializationError,
    error: initializationError
  } = useQuery({
    queryKey: ['conversation', 'start', topicId],
    queryFn: () => startConversation(parseInt(topicId || '0')),
    enabled: !!topicId,
    retry: 2,
  });

  // Handle conversation data
  useEffect(() => {
    if (conversationData && !sessionId) {
      setSessionId(conversationData.session_id);
      setMessages([conversationData.initial_message]);
      setError(null);
    }
  }, [conversationData, sessionId]);

  // Handle initialization error
  useEffect(() => {
    if (hasInitializationError) {
      console.error('Error starting conversation:', initializationError);
      setError('Failed to start conversation. Please try again.');
    }
  }, [hasInitializationError, initializationError]);

  // Respond to conversation mutation
  const respondMutation = useMutation({
    mutationFn: ({ sessionId, request }: { sessionId: string; request: ConversationRespondRequest }) =>
      respondToConversation(sessionId, request),
    onSuccess: (data, variables) => {
      const userMessage: ConversationMessage = {
        id: Date.now(),
        session_id: variables.sessionId,
        message_type: 'user',
        content: data.transcribed_text || variables.request.student_input_text || '[Audio message]',
        transcribed_text: data.transcribed_text,
        created_at: new Date().toISOString()
      };

      setMessages(prev => [...prev, userMessage, data.ai_response]);
      setTextInput('');
      setError(null);
    },
    onError: (error) => {
      console.error('Error processing response:', error);
      setError(error instanceof Error ? error.message : 'Failed to process response');
    }
  });

  // Handle audio recording completion
  const handleAudioRecorded = async (audioBlob: Blob) => {
    if (!sessionId) {
      setError('No active session');
      return;
    }

    try {
      setError(null);

      console.log('🎤 Audio recorded:', {
        size: audioBlob.size,
        type: audioBlob.type,
        timestamp: new Date().toISOString()
      });

      // Save audio for debugging (non-blocking)
      saveAudioForDebug(audioBlob).then(debugResult => {
        if (debugResult) {
          console.log('🔍 Audio saved for debug analysis:', debugResult);
        }
      }).catch(err => {
        console.warn('Debug audio save failed (non-critical):', err);
      });

      // Convert audio to base64
      const audioBase64 = await audioToBase64(audioBlob);
      const audioFormat = audioBlob.type.includes('webm') ? 'webm' :
                         audioBlob.type.includes('mp4') ? 'mp4' : 'webm';

      // Send audio to backend
      respondMutation.mutate({
        sessionId,
        request: {
          student_audio_base64: audioBase64,
          audio_format: audioFormat,
          language_code: 'en-US'
        }
      });

    } catch (error) {
      console.error('Error processing audio:', error);
      setError(error instanceof Error ? error.message : 'Failed to process audio');
    }
  };

  // Handle text input submission
  const handleTextSubmit = () => {
    if (!sessionId || !textInput.trim()) {
      return;
    }

    setError(null);
    respondMutation.mutate({
      sessionId,
      request: {
        student_input_text: textInput.trim()
      }
    });
  };

  // Handle audio recording error
  const handleAudioError = (errorMessage: string) => {
    setError(errorMessage);
  };

  // Handle microphone permission changes
  const handlePermissionChange = (state: MicrophonePermissionState) => {
    setMicrophonePermission(state);

    // Auto-switch to text mode if microphone is unavailable or denied
    if (state === 'unavailable' || state === 'denied') {
      if (inputMode === 'audio') {
        setInputMode('text');
        setError('Microphone access unavailable. Switched to text input mode.');
      }
    }
  };

  // Handle back navigation
  const handleBack = () => {
    navigate('/');
  };

  // Loading state
  if (isInitializing) {
    return (
      <div className="flex-1 flex items-center justify-center p-4">
        <div className="course-card max-w-md w-full text-center">
          <div className="icon-container language mx-auto mb-4">
            <LoadingSpinner className="w-6 h-6 text-white" />
          </div>
          <h2 className="heading-secondary mb-2">
            Starting Conversation...
          </h2>
          <p className="body-text">
            Preparing your practice session
          </p>
        </div>
      </div>
    );
  }

  // Error state
  if (hasInitializationError) {
    return (
      <div className="flex-1 flex items-center justify-center p-4">
        <div className="course-card max-w-md w-full text-center border-l-4 border-red-500">
          <div className="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <ExclamationTriangleIcon className="w-6 h-6 text-red-600" />
          </div>
          <h2 className="heading-secondary mb-2">
            Failed to Start Conversation
          </h2>
          <p className="body-text mb-6">
            {initializationError instanceof Error ? initializationError.message : 'Something went wrong'}
          </p>
          <div className="space-y-4">
            <button
              onClick={() => window.location.reload()}
              className="btn-primary w-full"
            >
              Try Again
            </button>
            <button
              onClick={handleBack}
              className="btn-primary w-full bg-neutral-medium-gray text-neutral-text hover:bg-neutral-dark-gray"
            >
              Back to Topics
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex-1 flex flex-col overflow-hidden">
      {/* Topic Header */}
      <div className="bg-gradient-language shadow-card-soft px-4 py-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center flex-1">
            <div className="bg-white/20 backdrop-blur-sm rounded-full p-2 mr-3">
              <span className="text-lg">💬</span>
            </div>
            <div className="flex-1">
              <h2 className="text-lg font-semibold text-white">
                {conversationData?.topic.title}
              </h2>
              <p className="text-sm text-white/80">
                {conversationData?.topic.category}
              </p>
            </div>
          </div>
          <div className="bg-white/20 backdrop-blur-sm rounded-full px-3 py-1">
            <span className="text-xs text-white font-medium">
              {sessionId?.slice(0, 8)}...
            </span>
          </div>
        </div>
      </div>

      {/* Messages Container */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {messages.map((message) => (
          <div
            key={message.id}
            className={`flex ${
              message.message_type === 'user' ? 'justify-end' : 'justify-start'
            }`}
          >
            <div
              className={`max-w-[80%] px-4 py-3 rounded-2xl shadow-card-soft ${
                message.message_type === 'user'
                  ? 'bg-gradient-language text-white'
                  : 'bg-white text-neutral-text border border-neutral-medium-gray'
              }`}
            >
              <p className="text-sm leading-relaxed">{message.content}</p>

              {/* Show transcription info for audio messages */}
              {message.transcribed_text && message.message_type === 'user' && (
                <div className="mt-2 pt-2 border-t border-white/20">
                  <p className="text-xs text-white/80 mb-1">🎤 Transcribed:</p>
                  <p className="text-xs text-white/80 italic">"{message.transcribed_text}"</p>
                </div>
              )}

              <p className={`text-xs mt-1 ${
                message.message_type === 'user'
                  ? 'text-white/70'
                  : 'text-neutral-dark-gray'
              }`}>
                {new Date(message.created_at).toLocaleTimeString()}
              </p>
            </div>
          </div>
        ))}
        <div ref={messagesEndRef} />
      </div>

      {/* Input Area */}
      <div className="bg-white shadow-card-soft border-t border-neutral-medium-gray p-4 safe-area-bottom">
        <div className="flex flex-col space-y-4">
          
          {/* Input Mode Toggle */}
          <div className="flex justify-center">
            <div className="flex gap-2 bg-neutral-light-gray rounded-lg p-1">
              <button
                onClick={() => setInputMode('audio')}
                disabled={microphonePermission === 'unavailable'}
                className={`px-4 py-2 rounded-md text-sm font-semibold transition-colors relative ${
                  inputMode === 'audio'
                    ? 'bg-primary-blue text-white shadow-card-soft'
                    : microphonePermission === 'unavailable'
                    ? 'text-neutral-dark-gray cursor-not-allowed bg-transparent'
                    : 'bg-transparent text-neutral-dark-gray hover:bg-white'
                }`}
              >
                🎤 Voice
                {microphonePermission === 'granted' && (
                  <span className="absolute -top-1 -right-1 w-3 h-3 bg-accent-green rounded-full"></span>
                )}
                {microphonePermission === 'denied' && (
                  <span className="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full"></span>
                )}
                {microphonePermission === 'checking' && (
                  <span className="absolute -top-1 -right-1 w-3 h-3 bg-secondary-yellow rounded-full animate-pulse"></span>
                )}
              </button>
              <button
                onClick={() => setInputMode('text')}
                className={`px-4 py-2 rounded-md text-sm font-semibold transition-colors ${
                  inputMode === 'text'
                    ? 'bg-primary-blue text-white shadow-card-soft'
                    : 'bg-transparent text-neutral-dark-gray hover:bg-white'
                }`}
              >
                ⌨️ Text
              </button>
            </div>
          </div>

          {/* Audio Input Mode */}
          {inputMode === 'audio' && (
            <div className="flex flex-col items-center space-y-4">
              <AudioRecorder
                onAudioRecorded={handleAudioRecorded}
                onError={handleAudioError}
                onPermissionChange={handlePermissionChange}
                disabled={respondMutation.isPending}
                maxDuration={60}
              />

              {respondMutation.isPending && (
                <div className="flex items-center space-x-2 text-primary-blue">
                  <LoadingSpinner className="w-4 h-4" />
                  <span className="text-sm font-medium">Processing your message...</span>
                </div>
              )}

              <p className="text-xs text-neutral-dark-gray text-center max-w-md">
                Tap the microphone to record your response. Your speech will be converted to text and sent to the AI.
              </p>
            </div>
          )}

          {/* Text Input Mode */}
          {inputMode === 'text' && (
            <div className="flex space-x-3">
              <input
                type="text"
                value={textInput}
                onChange={(e) => setTextInput(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && !respondMutation.isPending && handleTextSubmit()}
                placeholder="Type your response..."
                disabled={respondMutation.isPending}
                className="flex-1 px-4 py-3 border border-neutral-medium-gray rounded-lg bg-white text-neutral-text placeholder-neutral-dark-gray focus:outline-none focus:ring-2 focus:ring-primary-blue focus:border-transparent disabled:opacity-50 disabled:bg-neutral-light-gray"
              />
              <button
                onClick={handleTextSubmit}
                disabled={!textInput.trim() || respondMutation.isPending}
                className="btn-primary px-6 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {respondMutation.isPending ? (
                  <LoadingSpinner className="w-4 h-4" />
                ) : (
                  'Send'
                )}
              </button>
            </div>
          )}

          {/* Error Display */}
          {error && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-3">
              <div className="flex items-center">
                <div className="w-5 h-5 bg-red-100 rounded-full flex items-center justify-center mr-3">
                  <ExclamationTriangleIcon className="w-3 h-3 text-red-600" />
                </div>
                <p className="text-sm text-red-700 flex-1">{error}</p>
                <button
                  onClick={() => setError(null)}
                  className="ml-2 text-red-600 hover:text-red-800 w-6 h-6 flex items-center justify-center rounded-full hover:bg-red-100 transition-colors"
                >
                  ✕
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ConversationScreen;