import React from 'react';
import { useNavigate } from 'react-router-dom';
import { useQuery } from '@tanstack/react-query';
import { fetchTopics } from '../../services/api';
import { Topic } from '../../types/topic';
import { LoadingSpinner, ExclamationTriangleIcon, MicrophoneIcon, ChatBubbleLeftIcon } from '../ui/Icons';

const TopicSelectionScreen: React.FC = () => {
  const navigate = useNavigate();

  const {
    data: topics,
    isLoading,
    isError,
    error,
    refetch
  } = useQuery({
    queryKey: ['topics'],
    queryFn: fetchTopics,
    staleTime: 1000 * 60 * 5, // 5 minutes
  });

  const handleTopicSelect = (topic: Topic) => {
    navigate(`/conversation/${topic.id}`);
  };

  const handlePronunciationPractice = () => {
    navigate('/pronunciation');
  };

  if (isLoading) {
    return (
      <div className="flex-1 flex items-center justify-center p-4">
        <div className="course-card max-w-md w-full text-center">
          <LoadingSpinner className="w-12 h-12 text-primary-blue mx-auto mb-4" />
          <h2 className="heading-secondary mb-2">
            Loading Topics...
          </h2>
          <p className="body-text">
            Please wait while we load the conversation topics.
          </p>
        </div>
      </div>
    );
  }

  if (isError) {
    return (
      <div className="flex-1 flex items-center justify-center p-4">
        <div className="course-card max-w-md w-full text-center">
          <ExclamationTriangleIcon className="w-12 h-12 text-red-500 mx-auto mb-4" />
          <h2 className="heading-secondary mb-2">
            Failed to Load Topics
          </h2>
          <p className="body-text mb-6">
            {error instanceof Error ? error.message : 'Something went wrong while loading topics.'}
          </p>
          <button
            onClick={() => refetch()}
            className="btn-primary w-full"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="flex-1 overflow-y-auto">
      <div className="p-4 pb-safe-bottom">
        {/* Welcome Section */}
        <div className="learning-card text-center mb-6">
          <h2 className="heading-primary mb-2">
            Welcome to EALA
          </h2>
          <p className="text-white/90 text-lg">
            Your AI Language Learning Assistant
          </p>
          <div className="mt-4 flex justify-center">
            <div className="bg-secondary-yellow w-16 h-16 rounded-full flex items-center justify-center shadow-icon-container">
              <span className="text-2xl">🎓</span>
            </div>
          </div>
        </div>

        {/* Practice Mode Selection */}
        <div className="mb-6">
          <h3 className="heading-secondary mb-4">
            Choose Your Practice Mode
          </h3>
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <div className="course-card card-hover">
              <div className="flex items-center mb-3">
                <div className="icon-container language">
                  <ChatBubbleLeftIcon className="w-6 h-6" />
                </div>
                <h4 className="subject-title ml-3">
                  Conversation Practice
                </h4>
              </div>
              <p className="body-text text-sm mb-4">
                Practice natural conversations with AI on various topics
              </p>
              <p className="text-primary-blue font-medium text-sm">
                Choose a topic below ↓
              </p>
            </div>

            <div className="course-card card-hover">
              <div className="flex items-center mb-3">
                <div className="icon-container chemistry">
                  <MicrophoneIcon className="w-6 h-6" />
                </div>
                <h4 className="subject-title ml-3">
                  Pronunciation Practice
                </h4>
              </div>
              <p className="body-text text-sm mb-4">
                Practice pronunciation with AI-generated sentences
              </p>
              <button
                onClick={handlePronunciationPractice}
                className="btn-primary w-full"
              >
                Start Practice
              </button>
            </div>
          </div>
        </div>

        {/* Topics Section */}
        <div className="mb-6">
          <h3 className="heading-secondary mb-4">
            Conversation Topics
          </h3>

          {topics && topics.length > 0 ? (
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
              {topics.map((topic, index) => {
                const subjectTypes = ['language', 'mathematics', 'chemistry', 'biology'];
                const subjectType = subjectTypes[index % subjectTypes.length];
                return (
                  <div
                    key={topic.id}
                    onClick={() => handleTopicSelect(topic)}
                    className="subject-card card-hover cursor-pointer touch-target"
                  >
                    <div className="flex items-start justify-between mb-3">
                      <div className="flex items-start">
                        <div className={`icon-container ${subjectType} flex-shrink-0`}>
                          <ChatBubbleLeftIcon className="w-6 h-6" />
                        </div>
                        <div className="ml-3 flex-1">
                          <h4 className="subject-title line-clamp-2">
                            {topic.title}
                          </h4>
                          {topic.category && (
                            <span className="inline-block text-xs bg-secondary-yellow text-yellow-900 font-semibold px-2 py-1 rounded-full mt-1">
                              {topic.category}
                            </span>
                          )}
                        </div>
                      </div>
                    </div>
                    
                    {topic.description && (
                      <p className="body-text text-sm line-clamp-3 mb-4">
                        {topic.description}
                      </p>
                    )}
                    
                    <div className="flex items-center text-primary-blue font-medium text-sm">
                      <span>Start Conversation</span>
                      <span className="ml-1">→</span>
                    </div>
                  </div>
                );
              })}
            </div>
          ) : (
            <div className="course-card text-center">
              <ExclamationTriangleIcon className="w-12 h-12 text-neutral-dark-gray mx-auto mb-4" />
              <h4 className="heading-secondary mb-2">
                No Topics Available
              </h4>
              <p className="body-text mb-4">
                No conversation topics are available at the moment.
              </p>
              <button
                onClick={() => refetch()}
                className="btn-primary"
              >
                Refresh Topics
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default TopicSelectionScreen;