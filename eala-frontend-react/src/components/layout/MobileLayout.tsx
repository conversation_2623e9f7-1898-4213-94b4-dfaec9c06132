import React from 'react';
import { useLocation } from 'react-router-dom';
import MobileHeader from './MobileHeader';
import BottomTabBar from './BottomTabBar';

interface MobileLayoutProps {
  children: React.ReactNode;
}

const MobileLayout: React.FC<MobileLayoutProps> = ({ children }) => {
  const location = useLocation();
  const isConversationScreen = location.pathname.includes('/conversation/');

  return (
    <div className="min-h-screen bg-neutral-light-gray flex flex-col">
      {/* Mobile Header */}
      <MobileHeader />
      
      {/* Main Content */}
      <main className={`flex-1 flex flex-col overflow-hidden pt-20 ${!isConversationScreen ? 'pb-24' : ''}`}>
        {children}
      </main>
      
      {/* Bottom Navigation - Hide on conversation screen for more space */}
      {!isConversationScreen && <BottomTabBar />}
    </div>
  );
};

export default MobileLayout;