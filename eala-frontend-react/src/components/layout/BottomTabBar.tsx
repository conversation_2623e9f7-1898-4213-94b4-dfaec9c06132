import React from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { HomeIcon, MicrophoneIcon } from '../ui/Icons';

const BottomTabBar: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();

  const tabs = [
    {
      id: 'home',
      label: 'Topics',
      icon: HomeIcon,
      path: '/',
      isActive: location.pathname === '/',
    },
    {
      id: 'pronunciation',
      label: 'Pronunciation',
      icon: MicrophoneIcon,
      path: '/pronunciation',
      isActive: location.pathname.includes('/pronunciation'),
    },
  ];

  const handleTabPress = (path: string) => {
    navigate(path);
  };

  return (
    <div className="fixed bottom-0 left-0 right-0 z-50 pointer-events-none safe-area-bottom">
      <div className="flex justify-center px-4 pb-4">
        <nav className="bg-white/20 backdrop-blur-xl shadow-liquid-glass rounded-3xl border border-white/30 pointer-events-auto transform transition-all duration-300 ease-out animate-slide-up relative overflow-hidden">
          {/* Liquid glass background gradient overlay */}
          <div className="absolute inset-0 bg-gradient-to-r from-white/10 via-white/20 to-white/10 rounded-3xl"></div>
          
          {/* Inner glow effect */}
          <div className="absolute inset-[1px] bg-gradient-to-b from-white/20 to-transparent rounded-[calc(1.5rem-1px)] pointer-events-none"></div>
          
          <div className="flex items-center px-4 py-3 relative z-10">
            {tabs.map((tab) => {
              const Icon = tab.icon;
              return (
                <button
                  key={tab.id}
                  onClick={() => handleTabPress(tab.path)}
                  className={`tab-nav-liquid flex-1 ${tab.isActive ? 'active' : 'inactive'}`}
                  aria-label={tab.label}
                >
                  <Icon className={`w-6 h-6 mb-1 transition-all duration-300 ${tab.isActive ? 'text-white drop-shadow-sm' : 'text-neutral-dark-gray'}`} />
                  <span className={`text-xs font-medium transition-all duration-300 ${tab.isActive ? 'text-white drop-shadow-sm' : 'text-neutral-dark-gray'}`}>
                    {tab.label}
                  </span>
                </button>
              );
            })}
          </div>
        </nav>
      </div>
    </div>
  );
};

export default BottomTabBar;