import React from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { ArrowLeftIcon } from '../ui/Icons';

const MobileHeader: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();

  const isHomePage = location.pathname === '/';
  const isConversationPage = location.pathname.includes('/conversation/');
  const isPronunciationPage = location.pathname.includes('/pronunciation');

  const getPageInfo = () => {
    if (isHomePage) return {
      title: 'EALA',
      subtitle: 'AI Language Assistant',
      icon: '🎓',
      gradient: 'bg-gradient-primary-card'
    };
    if (isConversationPage) return {
      title: 'Conversation',
      subtitle: 'Practice speaking',
      icon: '💬',
      gradient: 'bg-gradient-language'
    };
    if (isPronunciationPage) return {
      title: 'Pronunciation',
      subtitle: 'Perfect your speech',
      icon: '🎤',
      gradient: 'bg-gradient-chemistry'
    };
    return {
      title: 'EALA',
      subtitle: 'AI Language Assistant',
      icon: '🎓',
      gradient: 'bg-gradient-primary-card'
    };
  };

  const handleBack = () => {
    if (isConversationPage || isPronunciationPage) {
      navigate('/');
    } else {
      navigate(-1);
    }
  };

  const pageInfo = getPageInfo();

  return (
    <header className={`${pageInfo.gradient} shadow-card-medium safe-area-top fixed top-0 left-0 right-0 z-50`}>
      <div className="flex items-center justify-between px-4 py-4">
        {/* Back Button */}
        {!isHomePage && (
          <button
            onClick={handleBack}
            className="flex items-center justify-center touch-target -ml-2 rounded-lg hover:bg-white/10 transition-colors"
            aria-label="Go back"
          >
            <ArrowLeftIcon className="w-6 h-6 text-white" />
          </button>
        )}
        
        {/* Title Section */}
        <div className={`flex items-center ${!isHomePage ? 'flex-1 justify-center -mr-touch' : ''}`}>
          <div className="bg-white/20 backdrop-blur-sm rounded-full p-2 mr-3">
            <span className="text-2xl" role="img" aria-label={pageInfo.title}>
              {pageInfo.icon}
            </span>
          </div>
          <div className="text-left">
            <h1 className="text-xl font-bold text-white leading-tight">
              {pageInfo.title}
            </h1>
            <p className="text-white/80 text-sm font-medium">
              {pageInfo.subtitle}
            </p>
          </div>
        </div>
        
        {/* Right side - Progress indicator or menu */}
        {isHomePage && (
          <div className="touch-target flex items-center justify-center">
            <div className="flex space-x-1">
              <div className="w-2 h-2 bg-white/60 rounded-full"></div>
              <div className="w-2 h-2 bg-white/40 rounded-full"></div>
              <div className="w-2 h-2 bg-white/40 rounded-full"></div>
            </div>
          </div>
        )}
      </div>
      
      {/* Decorative bottom border */}
      <div className="h-1 bg-gradient-to-r from-white/20 via-white/40 to-white/20"></div>
    </header>
  );
};

export default MobileHeader;