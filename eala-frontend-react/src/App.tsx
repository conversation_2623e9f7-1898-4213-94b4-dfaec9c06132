import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';
import TopicSelectionScreen from './components/screens/TopicSelectionScreen';
import ConversationScreen from './components/screens/ConversationScreen';
import PronunciationPractice from './components/screens/PronunciationPractice';
import MobileLayout from './components/layout/MobileLayout';
import ErrorBoundary from './components/ui/ErrorBoundary';

// Create a client for React Query
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 1000 * 60 * 5, // 5 minutes
      gcTime: 1000 * 60 * 10, // 10 minutes (formerly cacheTime)
      retry: (failureCount, error) => {
        // Don't retry on 4xx errors
        if (error && 'status' in error && typeof error.status === 'number') {
          return error.status >= 500 && failureCount < 3;
        }
        return failureCount < 3;
      },
      refetchOnWindowFocus: false,
      refetchOnMount: false,
      refetchOnReconnect: true,
    },
    mutations: {
      retry: 2,
    },
  },
});

function App() {
  return (
    <QueryClientProvider client={queryClient}>
      <ErrorBoundary>
        <Router>
          <MobileLayout>
            <Routes>
              <Route path="/" element={<TopicSelectionScreen />} />
              <Route path="/conversation/:topicId" element={<ConversationScreen />} />
              <Route path="/pronunciation" element={<PronunciationPractice />} />
              <Route path="*" element={<TopicSelectionScreen />} />
            </Routes>
          </MobileLayout>
        </Router>
      </ErrorBoundary>
      <ReactQueryDevtools initialIsOpen={false} />
    </QueryClientProvider>
  );
}

export default App;