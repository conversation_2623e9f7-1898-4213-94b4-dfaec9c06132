/** @type {import('tailwindcss').Config} */
export default {
  content: ["./index.html", "./src/**/*.{js,ts,jsx,tsx}"],
  theme: {
    extend: {
      colors: {
        // Design system color palette
        primary: {
          blue: "#6366F1",
          "dark-blue": "#4F46E5",
          "light-blue": "#818CF8",
        },
        secondary: {
          orange: "#F97316",
          coral: "#FF6B6B",
          yellow: "#FCD34D",
        },
        accent: {
          green: "#10B981",
          teal: "#14B8A6",
          purple: "#8B5CF6",
        },
        neutral: {
          white: "#FFFFFF",
          "light-gray": "#F8FAFC",
          "medium-gray": "#E2E8F0",
          "dark-gray": "#64748B",
          text: "#334155",
        },
        // Subject-specific colors
        subject: {
          mathematics: "#F97316",
          "mathematics-dark": "#DC2626",
          chemistry: "#10B981",
          "chemistry-dark": "#059669",
          biology: "#3B82F6",
          "biology-dark": "#1D4ED8",
          language: "#06B6D4",
          "language-dark": "#0891B2",
        },
        // Legacy compatibility colors
        background: "#F8FAFC",
        foreground: "#334155",
        blue: {
          400: "#60a5fa",
          500: "#3b82f6",
          600: "#2563eb",
        },
        yellow: {
          400: "#facc15",
          500: "#eab308",
        },
        red: {
          400: "#f87171",
          500: "#ef4444",
        },
        green: {
          400: "#4ade80",
          500: "#22c55e",
        },
        gray: {
          100: "#f3f4f6",
          200: "#e5e7eb",
          300: "#d1d5db",
          400: "#9ca3af",
          500: "#6b7280",
          600: "#4b5563",
          700: "#374151",
          800: "#1f2937",
          900: "#111827",
        },
        white: "#ffffff",
        black: "#000000",
      },
      backgroundImage: {
        // Design system gradients
        "gradient-primary-card":
          "linear-gradient(135deg, #6366F1 0%, #8B5CF6 100%)",
        "gradient-promotional":
          "linear-gradient(120deg, #6366F1 0%, #3B82F6 100%)",
        "gradient-button-primary":
          "linear-gradient(135deg, #F97316 0%, #EA580C 100%)",
        "gradient-button-primary-hover":
          "linear-gradient(135deg, #EA580C 0%, #DC2626 100%)",
        "gradient-unlock-button":
          "linear-gradient(135deg, #6366F1 0%, #8B5CF6 100%)",
        // Subject-specific gradients
        "gradient-mathematics":
          "linear-gradient(135deg, #F97316 0%, #DC2626 100%)",
        "gradient-chemistry":
          "linear-gradient(135deg, #10B981 0%, #059669 100%)",
        "gradient-biology": "linear-gradient(135deg, #3B82F6 0%, #1D4ED8 100%)",
        "gradient-language":
          "linear-gradient(135deg, #06B6D4 0%, #0891B2 100%)",
      },
      fontFamily: {
        sans: ["var(--font-geist-sans)", "Arial", "Helvetica", "sans-serif"],
        mono: ["var(--font-geist-mono)", "monospace"],
      },
      fontSize: {
        // Mobile-first typography scale - increased for better readability
        xs: ["14px", { lineHeight: "1.4" }],
        sm: ["16px", { lineHeight: "1.43" }],
        base: ["18px", { lineHeight: "1.5" }],
        lg: ["20px", { lineHeight: "1.44" }],
        xl: ["22px", { lineHeight: "1.4" }],
        "2xl": ["26px", { lineHeight: "1.33" }],
        "3xl": ["30px", { lineHeight: "1.2" }],
        "4xl": ["34px", { lineHeight: "1.2" }],
        "5xl": ["38px", { lineHeight: "1.1" }],
      },
      boxShadow: {
        // Design system shadows
        "card-soft": "0 4px 20px rgba(0, 0, 0, 0.08)",
        "card-medium": "0 6px 24px rgba(0, 0, 0, 0.1)",
        "card-strong": "0 8px 32px rgba(99, 102, 241, 0.2)",
        "button-default": "0 4px 16px rgba(249, 115, 22, 0.3)",
        "button-hover": "0 6px 20px rgba(249, 115, 22, 0.4)",
        "icon-container": "0 4px 16px rgba(252, 211, 77, 0.3)",
        "floating-icon": "0 2px 8px rgba(0, 0, 0, 0.1)",
        "floating-nav":
          "0 8px 32px rgba(0, 0, 0, 0.12), 0 2px 8px rgba(0, 0, 0, 0.08)",
        "liquid-glass":
          "0 8px 32px rgba(255, 255, 255, 0.1), 0 1px 1px rgba(255, 255, 255, 0.25), inset 0 1px 0 rgba(255, 255, 255, 0.15)",
        "tab-bar": "0 -2px 12px rgba(0, 0, 0, 0.08)",
        // Legacy brutal shadows
        brutal: "2px 2px 0px #000",
        "brutal-lg": "4px 4px 0px #000",
        "brutal-xl": "8px 8px 0px #000",
      },
      borderRadius: {
        // Design system border radius scale
        sm: "8px",
        md: "12px",
        lg: "16px",
        xl: "20px",
        "2xl": "24px",
      },
      animation: {
        "pulse-fast": "pulse 1s cubic-bezier(0.4, 0, 0.6, 1) infinite",
        "slide-up": "slideUp 0.3s ease-out",
      },
      keyframes: {
        slideUp: {
          "0%": { transform: "translateY(100%)", opacity: "0" },
          "100%": { transform: "translateY(0)", opacity: "1" },
        },
      },
      screens: {
        // Mobile-first breakpoints
        xs: "320px",
        sm: "640px",
        md: "768px",
        lg: "1024px",
        xl: "1280px",
        "2xl": "1536px",
      },
      spacing: {
        // Design system spacing
        touch: "44px", // Minimum touch target size
        "safe-top": "env(safe-area-inset-top)",
        "safe-bottom": "env(safe-area-inset-bottom)",
        "safe-left": "env(safe-area-inset-left)",
        "safe-right": "env(safe-area-inset-right)",
        // 8px grid system
        18: "4.5rem", // 72px
        22: "5.5rem", // 88px
        30: "7.5rem", // 120px
      },
    },
  },
  plugins: [],
};
