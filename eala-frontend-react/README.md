# EALA Frontend React

A mobile-first React + Vite frontend for the EALA (E-learning AI Language Assistant) application, designed for Capacitor mobile app deployment.

## Features

- 🚀 **React 19** with **Vite** for fast development and builds
- 📱 **Mobile-first responsive design** optimized for touch devices
- 🎨 **Tailwind CSS** with brutal design system
- 🔄 **React Router** for client-side navigation
- 📡 **TanStack Query** for API state management and caching
- 🎯 **TypeScript** for type safety
- 📲 **Capacitor** ready for iOS/Android deployment
- 🎤 **WebM audio recording** with mobile optimizations
- 🗣️ **Azure Speech Services** integration for pronunciation assessment
- 💬 **Real-time conversation** with AI language models

## Quick Start

```bash
# Install dependencies
npm install

# Start development server
npm run dev

# Build for production
npm run build

# Preview production build
npm run preview
```

## Project Structure

```
src/
├── components/
│   ├── screens/           # Main screen components
│   │   ├── TopicSelectionScreen.tsx
│   │   ├── ConversationScreen.tsx
│   │   └── PronunciationPractice.tsx
│   ├── ui/               # Reusable UI components
│   │   ├── AudioRecorder.tsx
│   │   ├── ErrorBoundary.tsx
│   │   ├── Icons.tsx
│   │   ├── PronunciationFeedback.tsx
│   │   └── PronunciationRecorder.tsx
│   └── layout/           # Layout components
│       ├── MobileLayout.tsx
│       ├── MobileHeader.tsx
│       └── BottomTabBar.tsx
├── services/
│   └── api.ts            # Enhanced API service with mobile optimizations
├── types/                # TypeScript type definitions
│   ├── conversation.ts
│   ├── pronunciation.ts
│   └── topic.ts
├── utils/
│   └── capacitor.ts      # Capacitor utilities for mobile features
└── hooks/                # Custom React hooks (future)
```

## Mobile-First Design

### Touch Interactions
- Minimum 44px touch targets for all interactive elements
- Optimized button states with visual feedback
- Swipe-friendly navigation
- Pull-to-refresh support

### Responsive Breakpoints
- **Mobile**: 320px - 767px (primary focus)
- **Tablet**: 768px - 1023px
- **Desktop**: 1024px+

### Safe Area Support
- Full support for notched devices (iPhone X+)
- Automatic safe area handling with CSS `env()` variables
- Status bar and navigation bar awareness

## API Integration

### Enhanced Features
- **Offline-first caching** for topics and conversations
- **Request debouncing** to prevent duplicate calls
- **Network-aware error handling** with retry logic
- **Request timeout management** for mobile networks
- **Audio validation** before processing

### Configuration
Set your API URL in `.env`:
```env
VITE_API_URL=http://localhost:8001
```

## Audio Features

### WebM Recording
- Optimized for mobile browsers
- Automatic format detection and fallback
- Quality validation before processing
- Debug audio saving for development

### Pronunciation Assessment
- Real-time audio processing
- Azure Speech Services integration
- Detailed word-level feedback
- Mobile-optimized UI for results

## Capacitor Mobile Deployment

### Initial Setup
```bash
# Initialize Capacitor
npx cap init

# Add platforms
npx cap add ios
npx cap add android

# Build and sync
npm run build
npx cap sync
```

### iOS Development
```bash
npx cap open ios
```

### Android Development
```bash
npx cap open android
```

### Mobile Features Available
- Native device information
- Haptic feedback
- Status bar control
- Keyboard management
- App state monitoring
- Deep linking support
- Native sharing

## Development Guidelines

### Code Style
- Use functional components with hooks
- Implement proper TypeScript typing
- Follow mobile-first CSS approach
- Use Tailwind utility classes
- Handle loading and error states

### Performance
- Lazy load heavy components
- Optimize images for mobile
- Use React Query for caching
- Minimize bundle size
- Implement service worker for offline support

### Accessibility
- Proper ARIA labels for touch targets
- Screen reader support
- High contrast mode compatibility
- Keyboard navigation support

## Environment Variables

```env
# Required
VITE_API_URL=http://localhost:8001

# Optional
VITE_NODE_ENV=development
```

## Building for Production

```bash
# Build optimized bundle
npm run build

# Test production build locally
npm run preview

# Build for Capacitor
npm run build && npx cap sync
```

## Troubleshooting

### Common Issues

1. **PostCSS Error**: Ensure `@tailwindcss/postcss` is installed
2. **Audio Recording**: Check microphone permissions in browser
3. **API Connection**: Verify API URL and CORS configuration
4. **Mobile Testing**: Use device simulators or real devices for touch testing

### Debug Mode

Audio recordings are automatically saved in debug mode for analysis:
```javascript
// Check browser console for debug info
console.log('🔍 Audio saved for debug analysis:', debugResult);
```

## Contributing

1. Follow the existing code structure and naming conventions
2. Test on both mobile and desktop viewports
3. Ensure all components are accessible
4. Update types when adding new features
5. Test audio functionality on real devices

## License

This project is part of the BMAD Method v3.1 and EALA application suite.