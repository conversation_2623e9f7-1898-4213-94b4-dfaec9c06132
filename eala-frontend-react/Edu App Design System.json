{"designSystemProfile": {"metadata": {"name": "Educational Learning App Design System", "theme": "Modern Educational Platform", "style": "Colorful, Friendly, Gradient-Heavy", "target": "Mobile-first educational interfaces"}, "colorPalette": {"primary": {"blue": "#6366F1", "darkBlue": "#4F46E5", "lightBlue": "#818CF8"}, "secondary": {"orange": "#F97316", "coral": "#FF6B6B", "yellow": "#FCD34D"}, "accent": {"green": "#10B981", "teal": "#14B8A6", "purple": "#8B5CF6"}, "neutral": {"white": "#FFFFFF", "lightGray": "#F8FAFC", "mediumGray": "#E2E8F0", "darkGray": "#64748B", "text": "#334155"}}, "gradients": {"primaryCard": {"background": "linear-gradient(135deg, #6366F1 0%, #8B5CF6 100%)", "direction": "135deg", "stops": ["#6366F1 0%", "#8B5CF6 100%"]}, "promotionalBanner": {"background": "linear-gradient(120deg, #6366F1 0%, #3B82F6 100%)", "direction": "120deg", "stops": ["#6366F1 0%", "#3B82F6 100%"]}, "subjectCard": {"mathematics": "linear-gradient(135deg, #F97316 0%, #DC2626 100%)", "chemistry": "linear-gradient(135deg, #10B981 0%, #059669 100%)", "biology": "linear-gradient(135deg, #3B82F6 0%, #1D4ED8 100%)", "language": "linear-gradient(135deg, #06B6D4 0%, #0891B2 100%)"}, "buttonPrimary": {"background": "linear-gradient(135deg, #F97316 0%, #EA580C 100%)", "hover": "linear-gradient(135deg, #EA580C 0%, #DC2626 100%)"}}, "elementStyling": {"cards": {"mainLearningCard": {"background": "linear-gradient(135deg, #6366F1 0%, #8B5CF6 100%)", "borderRadius": "24px", "padding": "32px 24px", "shadow": "0 8px 32px rgba(99, 102, 241, 0.2)", "border": "none", "textColor": "#FFFFFF", "iconContainer": {"background": "#FCD34D", "borderRadius": "50%", "size": "120px", "padding": "24px", "shadow": "0 4px 16px rgba(252, 211, 77, 0.3)"}, "decorativeElements": {"floatingIcons": {"background": "#FFFFFF", "borderRadius": "8px", "shadow": "0 2px 8px rgba(0, 0, 0, 0.1)", "size": "32px"}}}, "courseDiscoveryCard": {"background": "#FFFFFF", "borderRadius": "24px", "padding": "24px", "shadow": "0 4px 20px rgba(0, 0, 0, 0.08)", "border": "1px solid #F1F5F9"}, "subjectCards": {"background": "#FFFFFF", "borderRadius": "16px", "padding": "16px", "shadow": "0 2px 12px rgba(0, 0, 0, 0.06)", "border": "1px solid #F1F5F9", "iconContainer": {"mathematics": {"background": "linear-gradient(135deg, #F97316 0%, #DC2626 100%)", "borderRadius": "12px", "size": "48px", "iconColor": "#FFFFFF"}, "chemistry": {"background": "linear-gradient(135deg, #10B981 0%, #059669 100%)", "borderRadius": "12px", "size": "48px", "iconColor": "#FFFFFF"}, "biology": {"background": "linear-gradient(135deg, #3B82F6 0%, #1D4ED8 100%)", "borderRadius": "12px", "size": "48px", "iconColor": "#FFFFFF"}, "language": {"background": "linear-gradient(135deg, #06B6D4 0%, #0891B2 100%)", "borderRadius": "12px", "size": "48px", "iconColor": "#FFFFFF"}}}, "courseDetailCard": {"background": "#FFFFFF", "borderRadius": "20px", "padding": "0", "shadow": "0 6px 24px rgba(0, 0, 0, 0.1)", "border": "none", "header": {"background": "linear-gradient(135deg, #F97316 0%, #DC2626 100%)", "borderRadius": "20px 20px 0 0", "padding": "20px 24px", "textColor": "#FFFFFF"}, "content": {"background": "#FFFFFF", "padding": "24px", "textColor": "#334155"}}}, "buttons": {"primary": {"background": "linear-gradient(135deg, #F97316 0%, #EA580C 100%)", "color": "#FFFFFF", "borderRadius": "16px", "padding": "16px 32px", "border": "none", "fontSize": "16px", "fontWeight": "600", "shadow": "0 4px 16px rgba(249, 115, 22, 0.3)", "hover": {"background": "linear-gradient(135deg, #EA580C 0%, #DC2626 100%)", "transform": "translateY(-2px)", "shadow": "0 6px 20px rgba(249, 115, 22, 0.4)"}}, "promotional": {"background": "#FCD34D", "color": "#92400E", "borderRadius": "12px", "padding": "12px 24px", "border": "none", "fontSize": "14px", "fontWeight": "600", "shadow": "0 2px 8px rgba(252, 211, 77, 0.3)"}, "unlock": {"background": "linear-gradient(135deg, #6366F1 0%, #8B5CF6 100%)", "color": "#FFFFFF", "borderRadius": "16px", "padding": "16px 24px", "border": "none", "fontSize": "16px", "fontWeight": "600", "iconColor": "#FFFFFF"}}, "banners": {"promotional": {"background": "linear-gradient(120deg, #6366F1 0%, #3B82F6 100%)", "borderRadius": "16px", "padding": "20px", "textColor": "#FFFFFF", "decorativeElements": {"background": "#FCD34D", "borderRadius": "8px"}}}, "navigation": {"tabBar": {"background": "#FFFFFF", "borderRadius": "0", "shadow": "0 -2px 12px rgba(0, 0, 0, 0.08)", "padding": "16px 0", "iconColor": "#94A3B8", "activeIconColor": "#6366F1", "iconSize": "24px"}}, "backgrounds": {"screenBackground": "#F8FAFC", "containerBackground": "#FFFFFF"}, "typography": {"headingPrimary": {"color": "#FFFFFF", "fontSize": "28px", "fontWeight": "700", "lineHeight": "1.2"}, "headingSecondary": {"color": "#334155", "fontSize": "24px", "fontWeight": "600", "lineHeight": "1.3"}, "bodyText": {"color": "#64748B", "fontSize": "16px", "fontWeight": "400", "lineHeight": "1.5"}, "captionText": {"color": "#94A3B8", "fontSize": "14px", "fontWeight": "500"}, "priceText": {"color": "#FFFFFF", "fontSize": "20px", "fontWeight": "700"}, "subjectTitle": {"color": "#334155", "fontSize": "16px", "fontWeight": "600"}, "subjectPrice": {"color": "#64748B", "fontSize": "14px", "fontWeight": "500"}}, "icons": {"decorativeFloating": {"background": "#FFFFFF", "borderRadius": "8px", "shadow": "0 2px 8px rgba(0, 0, 0, 0.1)", "padding": "8px", "size": "32px"}, "subjectIcons": {"size": "24px", "color": "#FFFFFF"}, "navigationIcons": {"size": "24px", "default": "#94A3B8", "active": "#6366F1"}}, "effects": {"shadows": {"cardSoft": "0 4px 20px rgba(0, 0, 0, 0.08)", "cardMedium": "0 6px 24px rgba(0, 0, 0, 0.1)", "cardStrong": "0 8px 32px rgba(99, 102, 241, 0.2)", "buttonDefault": "0 4px 16px rgba(249, 115, 22, 0.3)", "buttonHover": "0 6px 20px rgba(249, 115, 22, 0.4)"}, "borderRadius": {"small": "8px", "medium": "12px", "large": "16px", "extraLarge": "20px", "hero": "24px"}}}, "componentStates": {"cards": {"default": "Apply base styling as defined", "hover": {"transform": "translateY(-2px)", "shadow": "Enhanced shadow by 20%"}}, "buttons": {"default": "Apply base styling as defined", "hover": "Apply hover object properties", "pressed": {"transform": "scale(0.98)", "shadow": "Reduced by 50%"}, "disabled": {"background": "#E2E8F0", "color": "#94A3B8", "shadow": "none"}}}, "doNotRules": {"gradients": ["DO NOT apply card background gradients to icons", "DO NOT use button gradients on text elements", "DO NOT apply promotional banner gradients to navigation", "DO NOT use subject-specific gradients outside their context"], "colors": ["DO NOT use white text on light backgrounds", "DO NOT apply icon container colors to card backgrounds", "DO NOT use navigation colors for content text", "DO NOT mix subject-specific color schemes"], "effects": ["DO NOT apply card shadows to icons", "DO NOT use button shadows on text elements", "DO NOT apply hover transforms to static elements", "DO NOT use hero border radius on small elements"]}, "implementationGuidelines": {"colorContrast": "Ensure minimum 4.5:1 contrast ratio for text", "gradientDirection": "Use 135deg for cards, 120deg for banners", "shadowOpacity": "Keep shadows subtle with low opacity (0.06-0.2)", "borderRadius": "Use consistent radius scale across similar elements", "spacing": "Maintain 8px grid system for padding and margins"}}}