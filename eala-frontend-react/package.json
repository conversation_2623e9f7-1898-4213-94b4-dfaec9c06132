{"name": "eala-frontend-react", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "clean": "rm -rf dist node_modules/.vite", "format": "prettier --write .", "test": "echo 'No tests configured for eala-frontend-react'"}, "dependencies": {"@capacitor/android": "^7.4.2", "@capacitor/app": "^7.0.1", "@capacitor/core": "^7.4.2", "@capacitor/device": "^7.0.1", "@capacitor/haptics": "^7.0.1", "@capacitor/ios": "^7.4.2", "@capacitor/keyboard": "^7.0.1", "@capacitor/share": "^7.0.1", "@capacitor/status-bar": "^7.0.1", "@tanstack/react-query": "^5.81.5", "@tanstack/react-query-devtools": "^5.81.5", "@types/react-router-dom": "^5.3.3", "framer-motion": "^12.23.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router-dom": "^7.6.3"}, "devDependencies": {"@capacitor/cli": "^7.4.2", "@eslint/js": "^9.30.1", "@tailwindcss/typography": "^0.5.16", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.6.0", "autoprefixer": "^10.4.21", "eslint": "^9.30.1", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "postcss": "^8.5.6", "tailwindcss": "^3.4.0", "typescript": "~5.8.3", "typescript-eslint": "^8.35.1", "vite": "^7.0.3"}}